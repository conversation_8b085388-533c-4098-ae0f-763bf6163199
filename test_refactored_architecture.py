#!/usr/bin/env python3
"""
Test script for the refactored architecture

This script tests the new architecture where:
1. Image processing operations are handled by restoration/flux.1
2. Image analysis is handled by perception/llava-vision
3. Action planning is handled by planning/LLM

测试重构后架构的脚本
"""

import sys
import logging
from pathlib import Path
from PIL import Image
import numpy as np

# Add the current directory to the path
sys.path.insert(0, str(Path(__file__).parent))

from autocompose.core.profile import Profile
from autocompose.agents.perception import PerceptionAgent
from autocompose.agents.planning import PlanningAgent
from autocompose.agents.restoration import RestorationAgent
from autocompose.agents.vision_guided_planner import VisionGuidedPlanner
from autocompose.utils.logging_config import setup_logging

def create_test_image():
    """Create a simple test image for testing"""
    # Create a simple RGB image
    width, height = 512, 512
    image_array = np.random.randint(0, 255, (height, width, 3), dtype=np.uint8)
    
    # Add some structure to make it more realistic
    # Add a gradient
    for i in range(height):
        image_array[i, :, 0] = np.clip(image_array[i, :, 0] + i // 4, 0, 255)
    
    # Add some "subject" in the center
    center_x, center_y = width // 2, height // 2
    for i in range(center_y - 50, center_y + 50):
        for j in range(center_x - 50, center_x + 50):
            if 0 <= i < height and 0 <= j < width:
                image_array[i, j] = [200, 150, 100]  # Brownish color for "subject"
    
    return Image.fromarray(image_array, 'RGB')

def test_perception_agent():
    """Test the Perception Agent functionality"""
    print("\n🔍 Testing Perception Agent...")
    print("=" * 50)
    
    try:
        # Create profile and agent
        profile = Profile()
        perception_agent = PerceptionAgent(profile)
        
        # Create test image
        test_image = create_test_image()
        
        # Test image properties analysis
        print("👁️  Testing image properties analysis...")
        properties = perception_agent.analyze_image_properties(test_image)
        
        print(f"✅ Image properties analyzed:")
        print(f"   - Content type: {properties['likely_content']}")
        print(f"   - Orientation: {properties['dominant_orientation']}")
        print(f"   - Size: {properties['width']}x{properties['height']}")
        print(f"   - Brightness: {properties['avg_brightness']:.1f}")
        print(f"   - Colorful: {properties['is_colorful']}")
        
        # Test content-aware recommendations
        print("\n💡 Testing content-aware recommendations...")
        recommendations = perception_agent.generate_content_aware_recommendations(test_image)
        
        print(f"✅ Generated {len(recommendations)} recommendations:")
        for i, rec in enumerate(recommendations[:3], 1):
            print(f"   {i}. {rec['name']} ({rec['category']}, {rec['priority']})")
            print(f"      📝 {rec['description']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Perception Agent test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_planning_agent():
    """Test the Planning Agent functionality"""
    print("\n🧠 Testing Planning Agent...")
    print("=" * 50)
    
    try:
        # Create profile and agent
        profile = Profile()
        planning_agent = PlanningAgent(profile)
        
        # Create test image properties
        test_properties = {
            "width": 512,
            "height": 512,
            "aspect_ratio": 1.0,
            "dominant_orientation": "square",
            "is_large": True,
            "likely_content": "portrait",
            "avg_brightness": 120,
            "is_colorful": True
        }
        
        # Test intelligent action generation
        print("🎯 Testing intelligent action generation...")
        actions = planning_agent.generate_intelligent_actions(
            image_properties=test_properties,
            max_actions=5
        )
        
        print(f"✅ Generated {len(actions)} intelligent actions:")
        for i, action in enumerate(actions, 1):
            print(f"   {i}. {action['name']} ({action['category']})")
            print(f"      📝 {action['description']}")
            print(f"      ⭐ Priority: {action.get('priority', 'N/A')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Planning Agent test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_restoration_agent():
    """Test the Restoration Agent functionality"""
    print("\n🎨 Testing Restoration Agent...")
    print("=" * 50)
    
    try:
        # Create profile and agent
        profile = Profile()
        restoration_agent = RestorationAgent(profile)
        
        # Create test image and action
        test_image = create_test_image()
        test_action = {
            "name": "enhance_colors",
            "description": "增强色彩饱和度和对比度",
            "category": "color_grading",
            "priority": "medium",
            "expected_effect": "使图像更加生动有趣"
        }
        
        # Test intelligent action application
        print("🖼️  Testing intelligent action application...")
        result_image = restoration_agent.apply_intelligent_action(test_image, test_action)
        
        print(f"✅ Action applied successfully:")
        print(f"   - Original size: {test_image.size}")
        print(f"   - Result size: {result_image.size}")
        print(f"   - Action: {test_action['name']}")
        print(f"   - Category: {test_action['category']}")
        
        # Save test result
        result_image.save("test_restoration_result.png")
        print(f"   - Result saved to: test_restoration_result.png")
        
        return True
        
    except Exception as e:
        print(f"❌ Restoration Agent test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_vision_guided_planner():
    """Test the Vision Guided Planner with new architecture"""
    print("\n🔍🧠 Testing Vision Guided Planner (New Architecture)...")
    print("=" * 60)
    
    try:
        # Create profile and planner
        profile = Profile()
        vision_planner = VisionGuidedPlanner(profile)
        
        # Create test image
        test_image = create_test_image()
        
        # Test vision-guided action generation
        print("🎯 Testing vision-guided action generation...")
        actions = vision_planner.generate_vision_guided_actions(test_image, max_actions=4)
        
        print(f"✅ Generated {len(actions)} vision-guided actions:")
        for i, action in enumerate(actions, 1):
            print(f"   {i}. {action['name']} ({action['category']})")
            print(f"      📝 {action['description']}")
            print(f"      ⭐ Priority: {action.get('priority', 'N/A')}")
            print(f"      💡 Reason: {action.get('reason', 'N/A')}")
        
        return True
        
    except Exception as e:
        print(f"❌ Vision Guided Planner test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_integration():
    """Test the integration of all components"""
    print("\n🔗 Testing Integration of All Components...")
    print("=" * 60)
    
    try:
        # Create profile and agents
        profile = Profile()
        perception_agent = PerceptionAgent(profile)
        planning_agent = PlanningAgent(profile)
        restoration_agent = RestorationAgent(profile)
        
        # Create test image
        test_image = create_test_image()
        
        # Step 1: Analyze image properties
        print("👁️  Step 1: Analyzing image properties...")
        properties = perception_agent.analyze_image_properties(test_image)
        print(f"   ✅ Content: {properties['likely_content']}, Orientation: {properties['dominant_orientation']}")
        
        # Step 2: Generate intelligent actions
        print("🧠 Step 2: Generating intelligent actions...")
        actions = planning_agent.generate_intelligent_actions(
            image_properties=properties,
            max_actions=3
        )
        print(f"   ✅ Generated {len(actions)} actions")
        
        # Step 3: Apply first action
        if actions:
            print("🎨 Step 3: Applying first action...")
            first_action = actions[0]
            result_image = restoration_agent.apply_intelligent_action(test_image, first_action)
            print(f"   ✅ Applied action: {first_action['name']}")
            
            # Save integration test result
            result_image.save("test_integration_result.png")
            print(f"   ✅ Result saved to: test_integration_result.png")
        
        return True
        
    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all tests"""
    print("🧪 Testing Refactored Architecture")
    print("=" * 80)
    print("Testing the new architecture where:")
    print("  👁️  Perception Agent handles image analysis")
    print("  🧠 Planning Agent handles action generation")
    print("  🎨 Restoration Agent handles action execution via Flux.1")
    print("=" * 80)
    
    # Setup logging
    setup_logging(level=logging.INFO)
    
    # Run tests
    test_results = []
    
    test_results.append(("Perception Agent", test_perception_agent()))
    test_results.append(("Planning Agent", test_planning_agent()))
    test_results.append(("Restoration Agent", test_restoration_agent()))
    test_results.append(("Vision Guided Planner", test_vision_guided_planner()))
    test_results.append(("Integration", test_integration()))
    
    # Print summary
    print("\n📊 Test Summary")
    print("=" * 50)
    
    passed = 0
    total = len(test_results)
    
    for test_name, result in test_results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{test_name:25} {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! The refactored architecture is working correctly.")
    else:
        print("⚠️  Some tests failed. Please check the error messages above.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
