#!/usr/bin/env python3
"""
Test LLM Fix

Quick test to verify the LLM method call fix.
"""

import sys
import logging
from pathlib import Path

# Add the current directory to the path
sys.path.insert(0, str(Path(__file__).parent))

from autocompose.agents.vision_guided_planner import VisionGuidedPlanner
from autocompose.agents.planning import UnifiedLLMClient
from autocompose.core.profile import Profile
from autocompose.utils.logging_config import setup_logging
from PIL import Image

def test_llm_method_fix():
    """测试 LLM 方法调用修复"""
    print("=== Testing LLM Method Call Fix ===")
    
    # 设置详细日志
    setup_logging(level=logging.INFO)
    logging.getLogger('autocompose.agents.vision_guided_planner').setLevel(logging.INFO)
    logging.getLogger('autocompose.agents.planning').setLevel(logging.INFO)
    
    try:
        # 创建组件
        profile = Profile()
        llm_client = UnifiedLLMClient(profile)
        vision_planner = VisionGuidedPlanner(profile, llm_client)
        
        print(f"✅ VisionGuidedPlanner created successfully")
        
        # 检查 LLM 可用性
        if llm_client.is_available():
            print("✅ LLM client is available")
        else:
            print("⚠️  LLM client not available, will use fallback actions")
        
        # 创建测试图像
        test_image = Image.new('RGB', (800, 600), color='blue')
        print(f"📸 Created test image: {test_image.size}")
        
        # 测试动作生成
        print("\n🧠 Testing vision-guided action generation...")
        actions = vision_planner.generate_vision_guided_actions(test_image, max_actions=6)
        
        print(f"\n✅ Successfully generated {len(actions)} actions!")
        
        print("\n📋 Generated Actions:")
        for i, action in enumerate(actions, 1):
            print(f"{i}. 🎯 {action['name']} ({action['category']})")
            print(f"   📝 {action['description']}")
        
        # 检查多样性
        unique_actions = len(set(action['name'] for action in actions))
        categories = len(set(action['category'] for action in actions))
        
        print(f"\n📊 Diversity Check:")
        print(f"  Unique actions: {unique_actions}/{len(actions)}")
        print(f"  Categories: {categories}")
        
        if unique_actions >= 4 and categories >= 3:
            print("✅ Good diversity achieved!")
            return True
        else:
            print("⚠️  Limited diversity")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_direct_llm_query():
    """直接测试 LLM 查询"""
    print("\n=== Testing Direct LLM Query ===")
    
    try:
        profile = Profile()
        llm_client = UnifiedLLMClient(profile)
        
        if not llm_client.is_available():
            print("⚠️  LLM not available, skipping direct query test")
            return True
        
        # 简单的测试查询
        test_prompt = "请简单回答：什么是图像构图？"
        
        print(f"🧠 Testing LLM query with prompt: {test_prompt}")
        response = llm_client.query(test_prompt)
        
        if response:
            print(f"✅ LLM responded: {response[:100]}...")
            return True
        else:
            print("❌ LLM returned empty response")
            return False
            
    except Exception as e:
        print(f"❌ Direct LLM test failed: {e}")
        return False

if __name__ == "__main__":
    print("🔧 LLM Method Fix Test")
    print("=" * 40)
    
    tests = [
        ("Vision-Guided Action Generation", test_llm_method_fix),
        ("Direct LLM Query", test_direct_llm_query)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🧪 Running: {test_name}")
        result = test_func()
        results.append((test_name, result))
        print(f"Result: {'✅ PASS' if result else '❌ FAIL'}")
    
    print("\n" + "=" * 40)
    print("📊 Test Results:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(tests)} tests passed")
    
    if passed == len(tests):
        print("🎉 All tests passed! LLM method fix is working correctly.")
    else:
        print("⚠️  Some tests failed. Please check the error messages above.")
