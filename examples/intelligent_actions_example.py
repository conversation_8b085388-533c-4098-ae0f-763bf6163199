#!/usr/bin/env python3
"""
Intelligent Actions Example

This example demonstrates the new intelligent action generation system that creates
context-aware image editing operations based on image analysis and LLM guidance.
"""

import sys
import logging
from pathlib import Path

# Add the current directory to the path
sys.path.insert(0, str(Path(__file__).parent.parent))

from autocompose import AutoComposeAgent
from autocompose.agents.perception import PerceptionAgent
from autocompose.agents.planning import PlanningAgent
from autocompose.agents.restoration import RestorationAgent
from autocompose.core.profile import Profile
from autocompose.utils.logging_config import setup_logging
from PIL import Image

def demonstrate_intelligent_actions():
    """展示智能动作生成系统"""
    print("=== Intelligent Action Generation System Demo ===")
    
    # 设置详细日志
    setup_logging(level=logging.INFO)
    logging.getLogger('autocompose.agents.perception').setLevel(logging.INFO)
    logging.getLogger('autocompose.agents.planning').setLevel(logging.INFO)
    logging.getLogger('autocompose.agents.restoration').setLevel(logging.INFO)

    try:
        # 创建新架构的代理（使用默认配置）
        profile = Profile()
        perception_agent = PerceptionAgent(profile)
        planning_agent = PlanningAgent(profile)
        restoration_agent = RestorationAgent(profile)
        
        # 加载测试图像
        input_path = "input.png"  # 替换为实际图像路径
        
        if Path(input_path).exists():
            image = Image.open(input_path).convert("RGB")
            print(f"\n📸 Analyzing image: {input_path} (size: {image.size})")
            
            # 使用新架构生成智能动作
            print("\n🧠 Generating intelligent actions using new architecture...")

            # Step 1: 分析图像属性
            print("👁️  Step 1: Analyzing image properties...")
            image_properties = perception_agent.analyze_image_properties(image)

            # Step 2: 生成智能动作
            print("🎯 Step 2: Generating intelligent actions...")
            actions = planning_agent.generate_intelligent_actions(
                image_properties=image_properties,
                max_actions=6
            )
            
            print(f"\n📋 Generated {len(actions)} intelligent actions:")
            print("=" * 80)
            
            for i, action in enumerate(actions, 1):
                print(f"\n{i}. 🎯 {action['name']}")
                print(f"   📝 Description: {action['description']}")
                print(f"   🏷️  Category: {action['category']}")
                print(f"   🎪 Expected Effect: {action.get('expected_effect', 'N/A')}")
            
            # 演示应用一个动作
            if actions:
                print(f"\n🎨 Demonstrating action application...")
                print("=" * 80)
                
                test_action = actions[0]
                print(f"Applying: {test_action['name']}")

                # 使用restoration agent应用动作
                result_image = restoration_agent.apply_intelligent_action(image, test_action)
                
                # 保存结果
                output_path = f"output_intelligent_{test_action['name']}.png"
                result_image.save(output_path)
                print(f"✅ Result saved to: {output_path}")
                
        else:
            print(f"❌ Input image not found: {input_path}")
            print("Creating a demo with synthetic image analysis...")
            demonstrate_synthetic_analysis()
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

def demonstrate_synthetic_analysis():
    """演示合成图像分析（使用新架构）"""
    print("\n🔬 Synthetic Image Analysis Demo (New Architecture)")
    print("=" * 60)

    try:
        # 创建新架构的代理
        profile = Profile()
        planning_agent = PlanningAgent(profile)

        # 模拟不同类型的图像属性
        test_cases = [
            {
                "name": "Portrait Photo",
                "properties": {
                    "width": 800,
                    "height": 1200,
                    "aspect_ratio": 0.67,
                    "dominant_orientation": "portrait",
                    "is_large": True,
                    "likely_content": "portrait",
                    "avg_brightness": 120,
                    "is_colorful": True
                }
            },
            {
                "name": "Landscape Photo",
                "properties": {
                    "width": 1920,
                    "height": 1080,
                    "aspect_ratio": 1.78,
                    "dominant_orientation": "landscape",
                    "is_large": True,
                    "likely_content": "landscape",
                    "avg_brightness": 140,
                    "is_colorful": True
                }
            },
            {
                "name": "Square Social Media",
                "properties": {
                    "width": 1080,
                    "height": 1080,
                    "aspect_ratio": 1.0,
                    "dominant_orientation": "square",
                    "is_large": True,
                    "likely_content": "general",
                    "avg_brightness": 110,
                    "is_colorful": False
                }
            }
        ]

        for test_case in test_cases:
            print(f"\n📊 {test_case['name']} ({test_case['properties']['width']}x{test_case['properties']['height']})")
            print("-" * 50)

            # 使用新架构生成动作
            actions = planning_agent.generate_intelligent_actions(
                image_properties=test_case['properties'],
                max_actions=4
            )

            for action in actions:
                print(f"  🎯 {action['name']}")
                print(f"     📝 {action['description']}")
                print(f"     🏷️  Category: {action['category']}")
                print(f"     ⭐ Priority: {action['priority']}")
                print()

    except Exception as e:
        print(f"❌ Synthetic analysis error: {e}")
        import traceback
        traceback.print_exc()

def demonstrate_full_intelligent_processing():
    """演示完整的智能处理流程"""
    print("\n=== Full Intelligent Processing Demo ===")
    
    # 启用详细日志
    logging.getLogger('autocompose.agents.agent_r_mcts').setLevel(logging.INFO)
    logging.getLogger('autocompose.agents.pure_agent_r').setLevel(logging.INFO)
    
    try:
        # 创建 Agent-R 系统
        agent = AutoComposeAgent()
        
        input_path = "input.png"
        
        if Path(input_path).exists():
            print(f"\n🚀 Starting intelligent Agent-R processing...")
            print("This will show:")
            print("  🧠 Intelligent action generation based on image content")
            print("  🌳 MCTS tree exploration with context-aware actions")
            print("  🎨 Semantic-aware image processing")
            print("  📊 Detailed evaluation and selection process")
            
            result = agent.process_image(input_path, "output_intelligent_full.png")
            
            if result['agent_r']['success']:
                print(f"\n🎉 Intelligent processing completed!")
                print(f"  Best trajectory: {result['agent_r']['best_trajectory']['trajectory_id']}")
                print(f"  Final reward: {result['agent_r']['best_trajectory']['total_reward']:.3f}")
                print(f"  Processing time: {result['processing_time']:.2f}s")
            else:
                print("❌ Processing failed")
        else:
            print(f"❌ Input image not found: {input_path}")
            
    except Exception as e:
        print(f"❌ Error: {e}")

def show_action_categories():
    """展示动作类别（使用新架构）"""
    print("\n📚 Available Action Categories (New Architecture):")
    print("=" * 60)

    # 定义动作类别（从原来的IntelligentActionGenerator迁移）
    action_categories = {
        "composition": [
            "crop_rule_of_thirds",
            "crop_golden_ratio",
            "reframe_subject",
            "adjust_perspective",
            "center_subject",
            "create_leading_lines"
        ],
        "color_grading": [
            "enhance_warm_tones",
            "enhance_cool_tones",
            "increase_saturation",
            "desaturate_background",
            "color_grade_cinematic",
            "balance_white_point"
        ],
        "local_adjustments": [
            "brighten_subject",
            "darken_background",
            "enhance_eyes",
            "smooth_skin",
            "sharpen_details",
            "blur_background"
        ],
        "semantic_edits": [
            "remove_distractions",
            "enhance_main_subject",
            "adjust_lighting_mood",
            "improve_facial_features",
            "enhance_landscape_elements",
            "remove_unwanted_objects"
        ],
        "creative_effects": [
            "add_cinematic_look",
            "create_depth_of_field",
            "add_vignette_effect",
            "enhance_golden_hour",
            "create_dramatic_contrast",
            "apply_artistic_style"
        ]
    }

    for category, actions in action_categories.items():
        print(f"\n🏷️  {category.upper().replace('_', ' ')}")
        for action in actions:
            print(f"  • {action}")

    print(f"\n💡 Note: These actions are now processed by:")
    print(f"  👁️  Perception Agent - for image analysis")
    print(f"  🧠 Planning Agent - for intelligent action generation")
    print(f"  🎨 Restoration Agent - for action execution via Flux.1")

if __name__ == "__main__":
    demonstrate_intelligent_actions()
    show_action_categories()
    
    print("\n" + "=" * 80)
    print("💡 Key Features of the Intelligent Action System:")
    print("  🧠 Context-aware action generation based on image analysis")
    print("  🎯 Semantic understanding of image content (portrait/landscape/general)")
    print("  🎨 Advanced image processing operations (composition, color, local edits)")
    print("  🤖 LLM-guided action suggestions (when LLM client is available)")
    print("  📊 Detailed logging of all operations and their effects")
    print("  🔄 Integration with MCTS for intelligent trajectory exploration")
    print("\n🚀 Try running with --show-tree --show-prompts for maximum detail!")
    print("=" * 80)
