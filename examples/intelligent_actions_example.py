#!/usr/bin/env python3
"""
Intelligent Actions Example

This example demonstrates the new intelligent action generation system that creates
context-aware image editing operations based on image analysis and LLM guidance.
"""

import sys
import logging
from pathlib import Path

# Add the current directory to the path
sys.path.insert(0, str(Path(__file__).parent.parent))

from autocompose import AutoComposeAgent
from autocompose.agents.intelligent_action_generator import IntelligentActionGenerator
from autocompose.agents.intelligent_image_processor import IntelligentImageProcessor
from autocompose.utils.logging_config import setup_logging
from PIL import Image

def demonstrate_intelligent_actions():
    """展示智能动作生成系统"""
    print("=== Intelligent Action Generation System Demo ===")
    
    # 设置详细日志
    setup_logging(level=logging.INFO)
    logging.getLogger('autocompose.agents.intelligent_action_generator').setLevel(logging.INFO)
    logging.getLogger('autocompose.agents.intelligent_image_processor').setLevel(logging.INFO)
    
    try:
        # 创建智能动作生成器
        action_generator = IntelligentActionGenerator()
        image_processor = IntelligentImageProcessor()
        
        # 加载测试图像
        input_path = "input.png"  # 替换为实际图像路径
        
        if Path(input_path).exists():
            image = Image.open(input_path).convert("RGB")
            print(f"\n📸 Analyzing image: {input_path} (size: {image.size})")
            
            # 生成智能动作
            print("\n🧠 Generating intelligent actions...")
            actions = action_generator.generate_context_aware_actions(image, max_actions=6)
            
            print(f"\n📋 Generated {len(actions)} intelligent actions:")
            print("=" * 80)
            
            for i, action in enumerate(actions, 1):
                print(f"\n{i}. 🎯 {action['name']}")
                print(f"   📝 Description: {action['description']}")
                print(f"   🏷️  Category: {action['category']}")
                print(f"   🎪 Expected Effect: {action.get('expected_effect', 'N/A')}")
            
            # 演示应用一个动作
            if actions:
                print(f"\n🎨 Demonstrating action application...")
                print("=" * 80)
                
                test_action = actions[0]
                print(f"Applying: {test_action['name']}")
                
                result_image = image_processor.apply_intelligent_action(image, test_action)
                
                # 保存结果
                output_path = f"output_intelligent_{test_action['name']}.png"
                result_image.save(output_path)
                print(f"✅ Result saved to: {output_path}")
                
        else:
            print(f"❌ Input image not found: {input_path}")
            print("Creating a demo with synthetic image analysis...")
            demonstrate_synthetic_analysis()
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

def demonstrate_synthetic_analysis():
    """演示合成图像分析"""
    print("\n🔬 Synthetic Image Analysis Demo")
    print("=" * 50)
    
    # 模拟不同类型的图像分析
    test_cases = [
        {
            "name": "Portrait Photo",
            "size": (800, 1200),
            "analysis": {
                "aspect_ratio": 0.67,
                "is_portrait": True,
                "is_landscape": False,
                "likely_content": "portrait"
            }
        },
        {
            "name": "Landscape Photo", 
            "size": (1920, 1080),
            "analysis": {
                "aspect_ratio": 1.78,
                "is_portrait": False,
                "is_landscape": True,
                "likely_content": "landscape"
            }
        },
        {
            "name": "Square Social Media",
            "size": (1080, 1080),
            "analysis": {
                "aspect_ratio": 1.0,
                "is_portrait": False,
                "is_landscape": False,
                "is_square": True,
                "likely_content": "general"
            }
        }
    ]
    
    action_generator = IntelligentActionGenerator()
    
    for test_case in test_cases:
        print(f"\n📊 {test_case['name']} ({test_case['size'][0]}x{test_case['size'][1]})")
        print("-" * 40)
        
        # 生成基于规则的动作
        actions = action_generator._generate_rule_based_actions(test_case['analysis'])
        
        for action in actions:
            print(f"  🎯 {action['name']}")
            print(f"     {action['description']}")

def demonstrate_full_intelligent_processing():
    """演示完整的智能处理流程"""
    print("\n=== Full Intelligent Processing Demo ===")
    
    # 启用详细日志
    logging.getLogger('autocompose.agents.agent_r_mcts').setLevel(logging.INFO)
    logging.getLogger('autocompose.agents.pure_agent_r').setLevel(logging.INFO)
    
    try:
        # 创建 Agent-R 系统
        agent = AutoComposeAgent()
        
        input_path = "input.png"
        
        if Path(input_path).exists():
            print(f"\n🚀 Starting intelligent Agent-R processing...")
            print("This will show:")
            print("  🧠 Intelligent action generation based on image content")
            print("  🌳 MCTS tree exploration with context-aware actions")
            print("  🎨 Semantic-aware image processing")
            print("  📊 Detailed evaluation and selection process")
            
            result = agent.process_image(input_path, "output_intelligent_full.png")
            
            if result['agent_r']['success']:
                print(f"\n🎉 Intelligent processing completed!")
                print(f"  Best trajectory: {result['agent_r']['best_trajectory']['trajectory_id']}")
                print(f"  Final reward: {result['agent_r']['best_trajectory']['total_reward']:.3f}")
                print(f"  Processing time: {result['processing_time']:.2f}s")
            else:
                print("❌ Processing failed")
        else:
            print(f"❌ Input image not found: {input_path}")
            
    except Exception as e:
        print(f"❌ Error: {e}")

def show_action_categories():
    """展示动作类别"""
    print("\n📚 Available Action Categories:")
    print("=" * 50)
    
    action_generator = IntelligentActionGenerator()
    
    for category, actions in action_generator.action_categories.items():
        print(f"\n🏷️  {category.upper().replace('_', ' ')}")
        for action in actions:
            print(f"  • {action}")

if __name__ == "__main__":
    demonstrate_intelligent_actions()
    show_action_categories()
    
    print("\n" + "=" * 80)
    print("💡 Key Features of the Intelligent Action System:")
    print("  🧠 Context-aware action generation based on image analysis")
    print("  🎯 Semantic understanding of image content (portrait/landscape/general)")
    print("  🎨 Advanced image processing operations (composition, color, local edits)")
    print("  🤖 LLM-guided action suggestions (when LLM client is available)")
    print("  📊 Detailed logging of all operations and their effects")
    print("  🔄 Integration with MCTS for intelligent trajectory exploration")
    print("\n🚀 Try running with --show-tree --show-prompts for maximum detail!")
    print("=" * 80)
