#!/usr/bin/env python3
"""
Vision-Guided Agent-R Example

This example demonstrates the new vision-guided Agent-R system that integrates:
1. LLaVA-Vision for deep image perception and analysis
2. LLM for intelligent action planning based on visual understanding
3. MCTS for trajectory exploration with vision-guided actions
"""

import sys
import logging
from pathlib import Path

# Add the current directory to the path
sys.path.insert(0, str(Path(__file__).parent.parent))

from autocompose import AutoComposeAgent
from autocompose.agents.vision_guided_planner import VisionGuidedPlanner
from autocompose.agents.planning import UnifiedLLMClient
from autocompose.utils.logging_config import setup_logging
from PIL import Image

def demonstrate_vision_guided_perception():
    """展示基于 LLaVA-Vision 的感知分析"""
    print("=== LLaVA-Vision Perception Analysis ===")
    
    # 设置详细日志
    setup_logging(level=logging.INFO)
    logging.getLogger('autocompose.agents.perception').setLevel(logging.INFO)
    logging.getLogger('autocompose.agents.vision_guided_planner').setLevel(logging.INFO)
    
    try:
        # 创建视觉引导规划器
        from autocompose.core.profile import Profile
        profile = Profile()  # 使用默认配置
        llm_client = UnifiedLLMClient(profile)
        
        vision_planner = VisionGuidedPlanner(profile, llm_client)
        
        input_path = "input.png"  # 替换为实际图像路径
        
        if Path(input_path).exists():
            image = Image.open(input_path).convert("RGB")
            print(f"\n📸 Analyzing image: {input_path} (size: {image.size})")
            
            # Step 1: 获取 LLaVA-Vision 感知摘要
            print("\n👁️  Step 1: LLaVA-Vision Perception Analysis")
            print("=" * 60)
            
            perception_summary = vision_planner.get_perception_summary(image)
            
            print(f"📝 Content Description:")
            print(f"   {perception_summary['content_description']}")
            
            print(f"\n🚨 Composition Issues ({perception_summary['issue_count']}):")
            for i, issue in enumerate(perception_summary['composition_issues'], 1):
                print(f"   {i}. {issue}")
            
            print(f"\n💡 Recommendations ({perception_summary['recommendation_count']}):")
            for i, rec in enumerate(perception_summary['recommendations'], 1):
                print(f"   {i}. {rec}")
            
            # Step 2: 生成基于视觉感知的智能动作
            print(f"\n🧠 Step 2: LLM-Guided Action Planning")
            print("=" * 60)
            
            actions = vision_planner.generate_vision_guided_actions(image, max_actions=6)
            
            print(f"\n📋 Generated {len(actions)} vision-guided actions:")
            for i, action in enumerate(actions, 1):
                print(f"\n{i}. 🎯 {action['name']}")
                print(f"   📝 Description: {action['description']}")
                print(f"   🏷️  Category: {action['category']}")
                print(f"   ⭐ Priority: {action['priority']}")
                print(f"   💡 Reason: {action.get('reason', 'N/A')}")
            
        else:
            print(f"❌ Input image not found: {input_path}")
            print("Please update the input_path variable to point to a valid image file.")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

def demonstrate_full_vision_guided_processing():
    """展示完整的视觉引导 Agent-R 处理流程"""
    print("\n=== Full Vision-Guided Agent-R Processing ===")
    
    # 启用详细日志
    logging.getLogger('autocompose.agents.agent_r_mcts').setLevel(logging.INFO)
    logging.getLogger('autocompose.agents.pure_agent_r').setLevel(logging.INFO)
    logging.getLogger('autocompose.agents.vision_guided_planner').setLevel(logging.INFO)
    
    try:
        # 创建 Agent-R 系统
        agent = AutoComposeAgent()
        
        input_path = "input.png"
        
        if Path(input_path).exists():
            print(f"\n🚀 Starting Vision-Guided Agent-R Processing...")
            print("This process includes:")
            print("  👁️  LLaVA-Vision deep image perception")
            print("  🧠 LLM-guided action planning based on visual analysis")
            print("  🌳 MCTS trajectory exploration with vision-guided actions")
            print("  🎨 Intelligent image processing with semantic understanding")
            print("  📊 Comprehensive evaluation and trajectory selection")
            
            print("\n" + "=" * 80)
            
            result = agent.process_image(input_path, "output_vision_guided.png")
            
            print("\n" + "=" * 80)
            
            if result['agent_r']['success']:
                print(f"🎉 Vision-Guided Processing Completed!")
                
                best_traj = result['agent_r']['best_trajectory']
                mcts_stats = result['agent_r']['mcts_stats']
                
                print(f"\n📊 Final Results:")
                print(f"  🏆 Best trajectory: {best_traj['trajectory_id']}")
                print(f"  💎 Final reward: {best_traj['total_reward']:.3f}")
                print(f"  📏 Trajectory length: {best_traj['length']}")
                print(f"  ⏱️  Processing time: {result['processing_time']:.2f}s")
                
                print(f"\n🌳 MCTS Statistics:")
                print(f"  🔄 Total rollbacks: {mcts_stats['total_rollbacks']}")
                print(f"  ✅ Successful trajectories: {mcts_stats['successful_trajectories']}")
                print(f"  ❌ Failed trajectories: {mcts_stats['failed_trajectories']}")
                
                # 轨迹分布
                traj_counts = result['agent_r']['trajectory_counts']
                print(f"\n🎯 Trajectory Distribution:")
                print(f"  ✅ Success: {traj_counts['success']}")
                print(f"  ❌ Failure: {traj_counts['failure']}")
                print(f"  🔄 Revision: {traj_counts['revision']}")
                print(f"  ⚪ Neutral: {traj_counts['neutral']}")
                
            else:
                print("❌ Vision-guided processing failed")
        else:
            print(f"❌ Input image not found: {input_path}")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

def show_vision_guided_features():
    """展示视觉引导系统的特性"""
    print("\n📚 Vision-Guided Agent-R System Features:")
    print("=" * 60)
    
    features = [
        {
            "component": "LLaVA-Vision Perception",
            "description": "Deep visual understanding of image content, composition, and quality",
            "capabilities": [
                "Content description and scene understanding",
                "Composition issue identification",
                "Quality assessment and improvement suggestions",
                "Semantic element recognition"
            ]
        },
        {
            "component": "LLM Action Planning", 
            "description": "Intelligent action generation based on visual analysis",
            "capabilities": [
                "Context-aware action suggestions",
                "Priority-based action ranking",
                "Reasoning-backed recommendations",
                "Category-specific optimizations"
            ]
        },
        {
            "component": "MCTS Integration",
            "description": "Tree search with vision-guided actions",
            "capabilities": [
                "Intelligent action space exploration",
                "Vision-informed rollback decisions",
                "Trajectory evaluation with visual feedback",
                "Adaptive search based on image content"
            ]
        }
    ]
    
    for feature in features:
        print(f"\n🔧 {feature['component']}")
        print(f"   📝 {feature['description']}")
        print(f"   🎯 Capabilities:")
        for cap in feature['capabilities']:
            print(f"      • {cap}")

def show_command_line_usage():
    """展示命令行使用方法"""
    print("\n💻 Command Line Usage Examples:")
    print("=" * 50)
    
    examples = [
        {
            "title": "Basic vision-guided processing",
            "command": "python run.py --input image.png --output enhanced.png"
        },
        {
            "title": "Show detailed vision analysis and MCTS tree",
            "command": "python run.py --input image.png --output enhanced.png --show-tree --verbose"
        },
        {
            "title": "Show LLM prompts and evaluation details",
            "command": "python run.py --input image.png --output enhanced.png --show-prompts"
        },
        {
            "title": "Full detailed output (vision + planning + tree + evaluation)",
            "command": "python run.py --input image.png --output enhanced.png --show-tree --show-prompts --verbose"
        },
        {
            "title": "Analysis only with vision-guided insights",
            "command": "python run.py --input image.png --output enhanced.png --analyze-only --show-tree"
        }
    ]
    
    for example in examples:
        print(f"\n📝 {example['title']}:")
        print(f"   {example['command']}")

if __name__ == "__main__":
    demonstrate_vision_guided_perception()
    demonstrate_full_vision_guided_processing()
    show_vision_guided_features()
    show_command_line_usage()
    
    print("\n" + "=" * 80)
    print("🎯 Vision-Guided Agent-R System Summary:")
    print("  👁️  Uses LLaVA-Vision for deep image understanding")
    print("  🧠 Employs LLM for intelligent action planning")
    print("  🌳 Integrates with MCTS for optimal trajectory exploration")
    print("  🎨 Applies semantic-aware image processing")
    print("  📊 Provides comprehensive analysis and feedback")
    print("\n🚀 This represents the most advanced image enhancement pipeline!")
    print("=" * 80)
