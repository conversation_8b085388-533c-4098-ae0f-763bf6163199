#!/usr/bin/env python3
"""
Detailed MCTS Process Example

This example demonstrates how to view the detailed MCTS tree construction process,
evaluation prompts, and trajectory selection in Agent-R.
"""

import sys
import logging
from pathlib import Path

# Add the current directory to the path
sys.path.insert(0, str(Path(__file__).parent.parent))

from autocompose import AutoComposeAgent
from autocompose.utils.logging_config import setup_logging

def detailed_mcts_example():
    """展示详细的 MCTS 过程"""
    print("=== Detailed Agent-R MCTS Process Example ===")
    
    # 设置详细日志
    setup_logging(level=logging.INFO)
    
    # 启用特定模块的详细日志
    logging.getLogger('autocompose.agents.agent_r_mcts').setLevel(logging.INFO)
    logging.getLogger('autocompose.agents.pure_agent_r').setLevel(logging.INFO)
    logging.getLogger('autocompose.core.evaluator').setLevel(logging.INFO)
    
    try:
        # 创建 Agent-R 系统
        agent = AutoComposeAgent()
        
        # 处理图像（这将显示详细的过程）
        input_path = "input.png"  # 替换为实际图像路径
        
        if Path(input_path).exists():
            print(f"\n🎬 Starting detailed MCTS processing for: {input_path}")
            print("=" * 80)
            
            result = agent.process_image(input_path, "output_detailed.png")
            
            print("\n" + "=" * 80)
            print("🎉 Processing completed!")
            
            if result['agent_r']['success']:
                best_traj = result['agent_r']['best_trajectory']
                mcts_stats = result['agent_r']['mcts_stats']
                
                print(f"\n📊 Final Results Summary:")
                print(f"  Best trajectory: {best_traj['trajectory_id']}")
                print(f"  Final reward: {best_traj['total_reward']:.3f}")
                print(f"  Trajectory length: {best_traj['length']}")
                print(f"  Total MCTS rollbacks: {mcts_stats['total_rollbacks']}")
                print(f"  Processing time: {result['processing_time']:.2f}s")
                
                # 轨迹分布
                traj_counts = result['agent_r']['trajectory_counts']
                print(f"\n🎯 Trajectory Distribution:")
                print(f"  Success: {traj_counts['success']}")
                print(f"  Failure: {traj_counts['failure']}")
                print(f"  Revision: {traj_counts['revision']}")
                print(f"  Neutral: {traj_counts['neutral']}")
                
            else:
                print("❌ Processing failed")
        else:
            print(f"❌ Input image not found: {input_path}")
            print("Please update the input_path variable to point to a valid image file.")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()

def command_line_examples():
    """展示命令行使用示例"""
    print("\n=== Command Line Usage Examples ===")
    
    examples = [
        {
            "title": "Basic processing",
            "command": "python run.py --input image.png --output enhanced.png"
        },
        {
            "title": "Show detailed MCTS tree construction",
            "command": "python run.py --input image.png --output enhanced.png --show-tree"
        },
        {
            "title": "Show evaluation prompts and scoring details",
            "command": "python run.py --input image.png --output enhanced.png --show-prompts"
        },
        {
            "title": "Show everything (tree + prompts + verbose)",
            "command": "python run.py --input image.png --output enhanced.png --show-tree --show-prompts --verbose"
        },
        {
            "title": "Analysis only with detailed tree",
            "command": "python run.py --input image.png --output enhanced.png --analyze-only --show-tree"
        }
    ]
    
    for example in examples:
        print(f"\n📝 {example['title']}:")
        print(f"   {example['command']}")

if __name__ == "__main__":
    detailed_mcts_example()
    command_line_examples()
    
    print("\n" + "=" * 80)
    print("💡 Tips:")
    print("  - Use --show-tree to see MCTS node expansion and tree structure")
    print("  - Use --show-prompts to see detailed evaluation scoring")
    print("  - Use --verbose for additional system information")
    print("  - Combine flags for maximum detail: --show-tree --show-prompts --verbose")
    print("=" * 80)
