#!/usr/bin/env python3
"""
Agent-R Integration Example
Agent-R 集成示例

This example demonstrates how to use the Agent-R integrated system
for intelligent image editing with reflection and trajectory collection.
"""

import sys
import logging
from pathlib import Path

# Add the parent directory to the path to import autocompose
sys.path.insert(0, str(Path(__file__).parent.parent))

from autocompose import AutoComposeAgent
from autocompose.core.profile import Profile

# Setup logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

logger = logging.getLogger(__name__)


def basic_agent_r_example():
    """基础 Agent-R 使用示例 - 纯 MCTS 和轨迹修正"""
    print("=== Pure Agent-R Example (MCTS + Trajectory Revision) ===")

    try:
        # Initialize AutoComposeAgent (now pure Agent-R by default)
        agent = AutoComposeAgent()

        # Process a single image with Agent-R MCTS
        image_path = "input.png"  # 替换为实际图像路径

        if Path(image_path).exists():
            result = agent.process_image(image_path, "output.png")

            print(f"Processing result: {result['agent_r']['success']}")
            print(f"Processing time: {result['processing_time']:.2f}s")

            if result['agent_r']['success']:
                best_traj = result['agent_r']['best_trajectory']
                print(f"Best trajectory: {best_traj['trajectory_id']}")
                print(f"Total reward: {best_traj['total_reward']:.3f}")
                print(f"Trajectory length: {best_traj['length']}")

                # MCTS 统计信息
                mcts_stats = result['agent_r']['mcts_stats']
                print(f"MCTS rollbacks: {mcts_stats['total_rollbacks']}")
                print(f"Successful trajectories: {mcts_stats['successful_trajectories']}")

                # 轨迹分类统计
                traj_counts = result['agent_r']['trajectory_counts']
                print(f"Trajectory counts: Success={traj_counts['success']}, "
                      f"Failure={traj_counts['failure']}, Revision={traj_counts['revision']}")
                print(f"Steps: {len(best_traj.steps)}")

                # Show trajectory steps
                print("\nTrajectory steps:")
                for i, step in enumerate(best_traj.steps):
                    print(f"  {i+1}. {step.action} (reward: {step.reward:+.3f})")

            # Show all trajectories summary
            if result.get('all_trajectories'):
                all_trajs = result['all_trajectories']
                print(f"\nAll trajectories generated:")
                print(f"  Success: {len(all_trajs.get('success', []))}")
                print(f"  Failure: {len(all_trajs.get('failure', []))}")
                print(f"  Revision: {len(all_trajs.get('revision', []))}")
                print(f"  Neutral: {len(all_trajs.get('neutral', []))}")

            # Show statistics
            if result.get('stats'):
                stats = result['stats']
                print(f"\nAgent-R Statistics:")
                print(f"  Total rollouts: {stats['total_rollouts']}")
                print(f"  Successful trajectories: {stats['successful_trajectories']}")
                print(f"  Revision trajectories: {stats['revision_trajectories']}")
        else:
            print(f"Image not found: {image_path}")
            print("Please update the image_path variable with a valid image file.")

    except Exception as e:
        logger.error(f"Basic example failed: {e}")


def batch_processing_example():
    """批量处理示例 - Agent-R MCTS"""
    print("\n=== Batch Processing with Agent-R MCTS ===")

    try:
        # Initialize AutoComposeAgent (now pure Agent-R by default)
        agent = AutoComposeAgent()

        # List of images to process
        image_paths = [
            "path/to/image1.jpg",
            "path/to/image2.jpg",
            "path/to/image3.jpg"
        ]

        # Filter existing images
        existing_images = [path for path in image_paths if Path(path).exists()]

        if existing_images:
            # Process each image individually using the new API
            results = []
            for i, image_path in enumerate(existing_images):
                print(f"Processing image {i+1}/{len(existing_images)}: {image_path}")
                result = agent.process_image(image_path, f"output_{i+1}.png")
                results.append(result)

            # Calculate batch statistics
            successful_count = sum(1 for r in results if r['agent_r']['success'])
            total_time = sum(r['processing_time'] for r in results)

            print(f"\nBatch processing completed:")
            print(f"  Total images: {len(results)}")
            print(f"  Successful: {successful_count}")
            print(f"  Success rate: {successful_count/len(results):.2%}")
            print(f"  Total processing time: {total_time:.2f}s")
            print(f"  Average time per image: {total_time/len(results):.2f}s")

            # Show MCTS statistics
            total_rollbacks = sum(r['agent_r']['mcts_stats']['total_rollbacks'] for r in results)
            total_success_trajs = sum(r['agent_r']['mcts_stats']['successful_trajectories'] for r in results)

            print(f"\nOverall MCTS Statistics:")
            print(f"  Total rollbacks: {total_rollbacks}")
            print(f"  Total successful trajectories: {total_success_trajs}")
        else:
            print("No valid images found. Please update the image_paths list.")

    except Exception as e:
        logger.error(f"Batch processing example failed: {e}")


def statistics_and_export_example():
    """统计信息和数据导出示例"""
    print("\n=== Statistics and Export Example ===")
    
    try:
        # Initialize AutoComposeAgent (now pure Agent-R by default)
        agent = AutoComposeAgent()
        
        # Get system statistics
        stats = agent.get_agent_r_statistics()
        if stats:
            print("Agent-R System Statistics:")
            session_stats = stats['session_stats']
            print(f"  Images processed: {session_stats['total_images_processed']}")
            print(f"  Successful edits: {session_stats['successful_edits']}")
            print(f"  Failed edits: {session_stats['failed_edits']}")
            print(f"  Reflections triggered: {session_stats['reflection_triggered']}")
            print(f"  Average improvement: {session_stats['average_improvement']:.3f}")
            
            # Export learning data
            export_path = "agent_r_learning_data.json"
            if agent.export_agent_r_data(export_path):
                print(f"\nLearning data exported to: {export_path}")
            else:
                print("\nFailed to export learning data")
        else:
            print("No statistics available (Agent-R not enabled or no data)")
            
    except Exception as e:
        logger.error(f"Statistics example failed: {e}")


def custom_configuration_example():
    """自定义配置示例"""
    print("\n=== Custom Configuration Example ===")
    
    try:
        # Create custom profile
        profile = Profile()
        
        # Enable specific features
        if hasattr(profile.profile, 'enable_thinking_planning'):
            profile.profile.enable_thinking_planning = True
        if hasattr(profile.profile, 'enable_mllm_restoration'):
            profile.profile.enable_mllm_restoration = True
        if hasattr(profile.profile, 'enable_planning_chain'):
            profile.profile.enable_planning_chain = True
        
        # Initialize with custom profile (Agent-R is now default)
        agent = AutoComposeAgent(config_path=profile)
        
        print("AutoComposeAgent initialized with custom configuration (pure Agent-R)")
        
        # You can now use the agent with custom settings
        # agent.process_image_with_reflection("your_image.jpg")
        
    except Exception as e:
        logger.error(f"Custom configuration example failed: {e}")


def main():
    """主函数"""
    print("Agent-R Integration Examples")
    print("=" * 50)
    
    # Run examples
    basic_agent_r_example()
    batch_processing_example()
    statistics_and_export_example()
    custom_configuration_example()
    
    print("\n" + "=" * 50)
    print("Examples completed!")
    print("\nNote: Update image paths in the examples to use your own images.")
    print("The pure Agent-R system uses MCTS for trajectory exploration with rollback,")
    print("trajectory classification, revision, and weighted voting for optimal results.")


if __name__ == "__main__":
    main()
