"""
Pure Agent-R Controller
纯 Agent-R 控制器

Minimal implementation focusing only on MCTS trajectory exploration with rollback
最小化实现，专注于带回滚机制的 MCTS 轨迹探索
"""

import logging
import time
from typing import List, Optional, Dict, Any
from PIL import Image

from .agent_r_mcts import AgentRMCTS, Trajectory, create_simple_evaluator
from .trajectory import TrajectoryReviser, WeightedVoting
from .reflection import ReflectionEngine
from .planning import UnifiedLLMClient
from ..core.evaluator import AestheticEvaluator

logger = logging.getLogger(__name__)


class PureAgentRController:
    """纯 Agent-R 控制器 - 只保留 MCTS 核心功能"""
    
    def __init__(self, llm_client: UnifiedLLMClient, aesthetic_evaluator: AestheticEvaluator, profile=None):
        self.llm_client = llm_client
        self.aesthetic_evaluator = aesthetic_evaluator
        self.profile = profile

        # 核心组件
        self.mcts = AgentRMCTS(max_iterations=50, max_depth=4, rollback_threshold=-0.3,
                              llm_client=llm_client, profile=profile)
        self.trajectory_reviser = TrajectoryReviser()
        self.reflection_engine = ReflectionEngine(llm_client)
        self.weighted_voting = WeightedVoting()
        
        # 评估函数
        self.evaluator = create_simple_evaluator(aesthetic_evaluator)
        
        logger.info("Pure Agent-R controller initialized")
    
    def process_image(self, image: Image.Image) -> Dict[str, Any]:
        """使用纯 Agent-R 方法处理图像"""
        start_time = time.time()
        
        try:
            logger.info("Starting pure Agent-R processing")
            
            # Phase 1: MCTS 轨迹探索
            trajectories = self.mcts.search(image, self.evaluator)
            
            # Phase 2: 轨迹分类和修正
            logger.info(f"🔍 Classifying {len(trajectories)} trajectories...")
            classified = self._classify_trajectories(trajectories)

            # Phase 3: 选择最佳轨迹
            logger.info(f"🎯 Selecting best trajectory from classified results...")
            best_trajectory = self._select_best_trajectory(classified)

            # Phase 4: 获取最终处理后的图像
            final_image = self._get_final_image(best_trajectory, image)

            processing_time = time.time() - start_time

            result = {
                'success': best_trajectory is not None,
                'best_trajectory': best_trajectory,
                'final_image': final_image,
                'all_trajectories': classified,
                'processing_time': processing_time,
                'mcts_stats': self.mcts.get_statistics()
            }
            
            logger.info(f"Agent-R processing completed in {processing_time:.2f}s")
            return result
            
        except Exception as e:
            logger.error(f"Agent-R processing failed: {e}")
            return {
                'success': False,
                'error': str(e),
                'processing_time': time.time() - start_time
            }
    
    def _classify_trajectories(self, trajectories: List[Trajectory]) -> Dict[str, List[Trajectory]]:
        """分类轨迹"""
        classified = {
            'success': [],
            'failure': [],
            'revision': [],
            'neutral': []
        }
        
        for traj in trajectories:
            if traj.total_reward > 0.2:
                classified['success'].append(traj)
            elif traj.total_reward < -0.2:
                classified['failure'].append(traj)
            else:
                classified['neutral'].append(traj)
        
        # 创建修正轨迹（简化版）
        if classified['failure'] and classified['success']:
            for i, failure_traj in enumerate(classified['failure'][:3]):  # 限制数量
                if classified['success']:
                    success_traj = classified['success'][0]
                    revision_traj = self._create_revision_trajectory(failure_traj, success_traj, i)
                    if revision_traj:
                        classified['revision'].append(revision_traj)
        
        logger.info(f"📊 Classification results:")
        logger.info(f"  ✅ Success: {len(classified['success'])} trajectories")
        logger.info(f"  ❌ Failure: {len(classified['failure'])} trajectories")
        logger.info(f"  🔄 Revision: {len(classified['revision'])} trajectories")
        logger.info(f"  ⚪ Neutral: {len(classified['neutral'])} trajectories")

        # 显示每个成功轨迹的详细信息
        if classified['success']:
            logger.info(f"🏆 Success trajectories details:")
            for traj in classified['success']:
                logger.info(f"  {traj.trajectory_id}: reward={traj.total_reward:.3f}, length={traj.length}")

        return classified

    def _get_final_image(self, best_trajectory: Optional[Trajectory],
                        original_image: Image.Image) -> Image.Image:
        """获取最终处理后的图像"""
        if not best_trajectory or not best_trajectory.nodes:
            logger.warning("No best trajectory found, returning original image")
            return original_image

        # 获取最佳轨迹的最后一个节点的图像状态
        final_node = best_trajectory.nodes[-1]
        if final_node.image_state:
            logger.info(f"Returning final image from trajectory {best_trajectory.trajectory_id}")
            return final_node.image_state
        else:
            logger.warning("Best trajectory has no final image state, returning original")
            return original_image

    def _create_revision_trajectory(self, failure_traj: Trajectory,
                                   success_traj: Trajectory, index: int) -> Optional[Trajectory]:
        """创建修正轨迹"""
        try:
            revision_traj = Trajectory(trajectory_id=f"revision_{index}")
            
            # 取失败轨迹的前半部分
            mid_point = len(failure_traj.nodes) // 2
            for node in failure_traj.nodes[:mid_point]:
                revision_traj.add_node(node)
            
            # 取成功轨迹的后半部分
            success_mid = len(success_traj.nodes) // 2
            for node in success_traj.nodes[success_mid:]:
                revision_traj.add_node(node)
            
            revision_traj.success = True
            return revision_traj
            
        except Exception as e:
            logger.warning(f"Failed to create revision trajectory: {e}")
            return None
    
    def _select_best_trajectory(self, classified: Dict[str, List[Trajectory]]) -> Optional[Trajectory]:
        """选择最佳轨迹"""
        logger.info(f"🎯 Trajectory selection process:")

        # 优先级：修正 > 成功 > 中性 > 失败
        candidates = []

        # 按优先级添加候选轨迹
        if classified.get('revision'):
            candidates.extend(classified['revision'])
            logger.info(f"  Added {len(classified['revision'])} revision trajectories (highest priority)")

        if classified.get('success'):
            candidates.extend(classified['success'])
            logger.info(f"  Added {len(classified['success'])} success trajectories")

        if classified.get('neutral'):
            candidates.extend(classified['neutral'])
            logger.info(f"  Added {len(classified['neutral'])} neutral trajectories")

        if not candidates and classified.get('failure'):
            candidates.extend(classified['failure'])
            logger.info(f"  Added {len(classified['failure'])} failure trajectories (fallback)")

        if not candidates:
            logger.warning("❌ No candidate trajectories found!")
            return None

        # 显示所有候选轨迹
        logger.info(f"📋 Candidate trajectories:")
        for traj in candidates:
            logger.info(f"  {traj.trajectory_id}: reward={traj.total_reward:.3f}, length={traj.length}")

        # 简单选择：按总奖励排序
        best = max(candidates, key=lambda t: t.total_reward)

        logger.info(f"🏆 Selected best trajectory: {best.trajectory_id} "
                   f"(reward: {best.total_reward:.3f}, length: {best.length})")

        return best
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取统计信息"""
        return {
            'mcts_stats': self.mcts.get_statistics(),
            'controller_type': 'pure_agent_r'
        }
    
    def reset(self):
        """重置控制器状态"""
        self.mcts.stats = {
            'total_rollbacks': 0,
            'successful_trajectories': 0,
            'failed_trajectories': 0
        }
        logger.info("Agent-R controller reset")


# 工厂函数和向后兼容
def create_agent_r_controller(llm_client: UnifiedLLMClient,
                             aesthetic_evaluator: AestheticEvaluator,
                             profile=None) -> PureAgentRController:
    return PureAgentRController(llm_client, aesthetic_evaluator, profile)

# 向后兼容别名
AgentRController = PureAgentRController
MasterController = PureAgentRController
UnifiedController = PureAgentRController
PlanningChainController = PureAgentRController
