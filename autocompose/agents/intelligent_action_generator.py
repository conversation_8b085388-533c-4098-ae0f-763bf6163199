"""
Intelligent Action Generator

Generates context-aware image editing actions based on image analysis and LLM guidance.
基于图像分析和LLM指导生成上下文感知的图像编辑动作。
"""

import logging
from typing import List, Dict, Any, Optional
from PIL import Image
import random

logger = logging.getLogger(__name__)


class IntelligentActionGenerator:
    """智能动作生成器"""
    
    def __init__(self, llm_client=None):
        self.llm_client = llm_client
        
        # 基础动作类别
        self.action_categories = {
            "composition": [
                "crop_rule_of_thirds",
                "crop_golden_ratio", 
                "reframe_subject",
                "adjust_perspective",
                "center_subject",
                "create_leading_lines"
            ],
            "color_grading": [
                "enhance_warm_tones",
                "enhance_cool_tones",
                "increase_saturation",
                "desaturate_background",
                "color_grade_cinematic",
                "balance_white_point"
            ],
            "local_adjustments": [
                "brighten_subject",
                "darken_background", 
                "enhance_eyes",
                "smooth_skin",
                "sharpen_details",
                "blur_background"
            ],
            "semantic_edits": [
                "remove_distractions",
                "enhance_main_subject",
                "adjust_lighting_mood",
                "improve_facial_features",
                "enhance_landscape_elements",
                "remove_unwanted_objects"
            ],
            "creative_effects": [
                "add_depth_of_field",
                "enhance_golden_hour",
                "create_dramatic_contrast",
                "add_vignette_effect",
                "enhance_texture_details",
                "improve_color_harmony"
            ]
        }
    
    def generate_context_aware_actions(self, image: Image.Image, 
                                     max_actions: int = 8) -> List[Dict[str, Any]]:
        """基于图像内容生成上下文感知的动作"""
        
        # 分析图像基本属性
        image_analysis = self._analyze_image_properties(image)
        
        # 生成动作建议
        if self.llm_client:
            suggested_actions = self._generate_llm_guided_actions(image, image_analysis)
        else:
            suggested_actions = self._generate_rule_based_actions(image_analysis)
        
        # 限制动作数量
        if len(suggested_actions) > max_actions:
            suggested_actions = suggested_actions[:max_actions]
        
        logger.info(f"🎯 Generated {len(suggested_actions)} context-aware actions")
        for action in suggested_actions:
            logger.info(f"  📝 {action['name']}: {action['description']}")
        
        return suggested_actions
    
    def _analyze_image_properties(self, image: Image.Image) -> Dict[str, Any]:
        """分析图像基本属性"""
        width, height = image.size
        aspect_ratio = width / height
        
        # 简单的图像分析
        analysis = {
            "size": (width, height),
            "aspect_ratio": aspect_ratio,
            "is_portrait": aspect_ratio < 1.0,
            "is_landscape": aspect_ratio > 1.3,
            "is_square": 0.9 <= aspect_ratio <= 1.1,
            "is_large": width * height > 1000000,  # > 1MP
            "dominant_orientation": "portrait" if aspect_ratio < 1.0 else "landscape"
        }
        
        # 基于尺寸推断可能的内容类型
        if analysis["is_portrait"] and not analysis["is_large"]:
            analysis["likely_content"] = "portrait"
        elif analysis["is_landscape"] and analysis["is_large"]:
            analysis["likely_content"] = "landscape"
        else:
            analysis["likely_content"] = "general"
        
        return analysis
    
    def _generate_llm_guided_actions(self, image: Image.Image, 
                                   analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """使用LLM生成智能动作建议"""
        
        prompt = self._create_action_generation_prompt(analysis)
        
        try:
            # 调用LLM生成建议
            response = self.llm_client.generate_response(prompt)
            actions = self._parse_llm_response(response)
            
            logger.info(f"🤖 LLM generated {len(actions)} intelligent actions")
            return actions
            
        except Exception as e:
            logger.warning(f"LLM action generation failed: {e}, falling back to rule-based")
            return self._generate_rule_based_actions(analysis)
    
    def _create_action_generation_prompt(self, analysis: Dict[str, Any]) -> str:
        """创建动作生成提示"""
        
        content_type = analysis["likely_content"]
        orientation = analysis["dominant_orientation"]
        size_desc = "large" if analysis["is_large"] else "medium"
        
        prompt = f"""
作为专业的图像编辑专家，请为一张{orientation}方向的{size_desc}尺寸{content_type}图像推荐6-8个具体的编辑动作。

图像信息：
- 尺寸: {analysis['size'][0]}x{analysis['size'][1]}
- 宽高比: {analysis['aspect_ratio']:.2f}
- 内容类型: {content_type}

请从以下角度提供具体的编辑建议：

1. 构图优化（如裁剪、重新构图、调整视角）
2. 色彩调整（如色调、饱和度、色彩平衡）
3. 局部调整（如主体增强、背景处理）
4. 语义编辑（如移除干扰物、增强特定元素）

每个建议请包含：
- 动作名称（简洁的英文标识符）
- 详细描述（具体要做什么）
- 预期效果（为什么这样做）

格式示例：
crop_rule_of_thirds: 使用三分法则重新裁剪图像，将主体放置在交叉点上 - 改善构图平衡
enhance_subject_lighting: 增强主体的光照，使其更加突出 - 提升视觉焦点
remove_background_distractions: 移除或模糊背景中的干扰元素 - 突出主体

请提供建议：
"""
        
        return prompt
    
    def _parse_llm_response(self, response: str) -> List[Dict[str, Any]]:
        """解析LLM响应"""
        actions = []
        
        lines = response.strip().split('\n')
        for line in lines:
            line = line.strip()
            if ':' in line and '-' in line:
                try:
                    # 解析格式：action_name: description - effect
                    parts = line.split(':', 1)
                    if len(parts) == 2:
                        action_name = parts[0].strip()
                        desc_effect = parts[1].strip()
                        
                        if '-' in desc_effect:
                            desc_parts = desc_effect.split('-', 1)
                            description = desc_parts[0].strip()
                            effect = desc_parts[1].strip() if len(desc_parts) > 1 else ""
                        else:
                            description = desc_effect
                            effect = ""
                        
                        actions.append({
                            "name": action_name,
                            "description": description,
                            "expected_effect": effect,
                            "category": self._categorize_action(action_name)
                        })
                except Exception as e:
                    logger.warning(f"Failed to parse action line: {line}, error: {e}")
                    continue
        
        return actions
    
    def _generate_rule_based_actions(self, analysis: Dict[str, Any]) -> List[Dict[str, Any]]:
        """基于规则生成动作建议"""
        actions = []
        content_type = analysis["likely_content"]
        
        # 根据内容类型选择合适的动作
        if content_type == "portrait":
            actions.extend([
                {
                    "name": "crop_portrait_composition",
                    "description": "使用肖像构图规则重新裁剪，突出面部特征",
                    "expected_effect": "改善肖像构图，突出主体",
                    "category": "composition"
                },
                {
                    "name": "enhance_skin_tones",
                    "description": "优化肤色，使其更加自然温暖",
                    "expected_effect": "提升肖像的自然美感",
                    "category": "color_grading"
                },
                {
                    "name": "brighten_eyes",
                    "description": "增强眼部亮度和清晰度",
                    "expected_effect": "使眼神更加有神",
                    "category": "local_adjustments"
                },
                {
                    "name": "blur_background_portrait",
                    "description": "模糊背景以突出人物主体",
                    "expected_effect": "创造景深效果，突出主体",
                    "category": "local_adjustments"
                }
            ])
        
        elif content_type == "landscape":
            actions.extend([
                {
                    "name": "crop_landscape_composition",
                    "description": "使用风景构图规则，强调地平线和前景",
                    "expected_effect": "改善风景构图的平衡感",
                    "category": "composition"
                },
                {
                    "name": "enhance_sky_drama",
                    "description": "增强天空的对比度和色彩饱和度",
                    "expected_effect": "创造更具戏剧性的天空效果",
                    "category": "color_grading"
                },
                {
                    "name": "sharpen_landscape_details",
                    "description": "锐化远景和细节，增强清晰度",
                    "expected_effect": "提升风景的细节表现",
                    "category": "local_adjustments"
                },
                {
                    "name": "enhance_natural_colors",
                    "description": "增强自然色彩，特别是绿色和蓝色",
                    "expected_effect": "使风景色彩更加鲜活",
                    "category": "color_grading"
                }
            ])
        
        else:  # general content
            actions.extend([
                {
                    "name": "improve_overall_composition",
                    "description": "根据构图原则重新裁剪和调整",
                    "expected_effect": "提升整体视觉平衡",
                    "category": "composition"
                },
                {
                    "name": "enhance_color_harmony",
                    "description": "调整色彩平衡，创造和谐的色调",
                    "expected_effect": "改善色彩的视觉舒适度",
                    "category": "color_grading"
                },
                {
                    "name": "increase_visual_impact",
                    "description": "增强对比度和清晰度",
                    "expected_effect": "提升图像的视觉冲击力",
                    "category": "local_adjustments"
                }
            ])
        
        # 添加一些通用的创意效果
        creative_actions = [
            {
                "name": "add_cinematic_look",
                "description": "添加电影级色彩分级效果",
                "expected_effect": "创造专业的电影感",
                "category": "creative_effects"
            },
            {
                "name": "enhance_mood_lighting",
                "description": "调整光照氛围，增强情感表达",
                "expected_effect": "提升图像的情感感染力",
                "category": "creative_effects"
            }
        ]
        
        # 随机选择一些创意效果
        actions.extend(random.sample(creative_actions, min(2, len(creative_actions))))
        
        return actions
    
    def _categorize_action(self, action_name: str) -> str:
        """根据动作名称推断类别"""
        action_lower = action_name.lower()
        
        if any(keyword in action_lower for keyword in ["crop", "composition", "frame", "perspective"]):
            return "composition"
        elif any(keyword in action_lower for keyword in ["color", "tone", "saturation", "hue", "balance"]):
            return "color_grading"
        elif any(keyword in action_lower for keyword in ["brighten", "darken", "enhance", "sharpen", "blur"]):
            return "local_adjustments"
        elif any(keyword in action_lower for keyword in ["remove", "add", "replace", "object"]):
            return "semantic_edits"
        else:
            return "creative_effects"
