"""
Trajectory Collection and Reflection Module
基于 Agent-R 的轨迹收集和反思机制

This module implements the core trajectory collection and reflection mechanisms
inspired by the Agent-R paper for image editing tasks.
"""

import time
import json
import logging
from typing import List, Dict, Optional, Any, Tuple
from dataclasses import dataclass, field
from enum import Enum
from pathlib import Path

from .unified_types import EditingInstruction, OperationResult, Priority

logger = logging.getLogger(__name__)


class TrajectoryStatus(Enum):
    """轨迹状态枚举"""
    SUCCESS = "success"
    FAILURE = "failure"
    PARTIAL = "partial"
    ABORTED = "aborted"


@dataclass
class TrajectoryStep:
    """轨迹步骤 - Agent-R 风格的动作-观察记录"""
    step_id: str
    action: str  # 编辑指令
    observation: float  # 评估分数
    reward: float  # 改进值
    is_error: bool = False  # 是否为错误步骤
    parent_id: Optional[str] = None  # 父节点ID（用于MCTS树结构）

    @property
    def is_success(self) -> bool:
        """判断步骤是否成功"""
        return self.reward >= 0 and not self.is_error

    @property
    def is_failure(self) -> bool:
        """判断步骤是否失败"""
        return self.reward < -0.1 or self.is_error


@dataclass
class EditingTrajectory:
    """编辑轨迹 - Agent-R 风格的动作序列"""
    trajectory_id: str
    image_path: str
    steps: List[TrajectoryStep] = field(default_factory=list)
    trajectory_type: str = "rollout"  # "rollout", "success", "failure", "revision"
    total_reward: float = 0.0
    first_error_step: Optional[int] = None  # 第一个错误步骤索引
    created_at: float = field(default_factory=time.time)

    def add_step(self, action: str, observation: float, reward: float,
                 parent_id: Optional[str] = None) -> str:
        """添加轨迹步骤 - 简化接口"""
        step_id = f"{self.trajectory_id}_step_{len(self.steps)}"
        step = TrajectoryStep(
            step_id=step_id,
            action=action,
            observation=observation,
            reward=reward,
            parent_id=parent_id
        )

        self.steps.append(step)
        self.total_reward += reward

        # 识别第一个错误步骤
        if step.is_failure and self.first_error_step is None:
            self.first_error_step = len(self.steps) - 1
            step.is_error = True

        return step_id
    
    def get_success_segments(self) -> List[List[TrajectoryStep]]:
        """提取成功的轨迹片段"""
        segments = []
        current_segment = []
        
        for step in self.steps:
            if step.is_success:
                current_segment.append(step)
            else:
                if current_segment:
                    segments.append(current_segment)
                    current_segment = []
        
        if current_segment:
            segments.append(current_segment)
        
        return segments
    
    def get_failure_segments(self) -> List[List[TrajectoryStep]]:
        """提取失败的轨迹片段"""
        segments = []
        current_segment = []
        
        for step in self.steps:
            if step.is_failure:
                current_segment.append(step)
            else:
                if current_segment:
                    segments.append(current_segment)
                    current_segment = []
        
        if current_segment:
            segments.append(current_segment)
        
        return segments
    
    def complete(self, status: TrajectoryStatus) -> None:
        """完成轨迹记录"""
        self.status = status
        self.completed_at = time.time()


class TrajectoryCollector:
    """轨迹收集器 - 基于 Agent-R 的轨迹收集机制"""
    
    def __init__(self, storage_path: str = "trajectories"):
        """初始化轨迹收集器"""
        self.storage_path = Path(storage_path)
        self.storage_path.mkdir(exist_ok=True)
        self.current_trajectory: Optional[EditingTrajectory] = None
        self.trajectory_history: List[EditingTrajectory] = []
    
    def start_trajectory(self, image_path: str) -> str:
        """开始新的轨迹收集"""
        trajectory_id = f"traj_{int(time.time())}_{hash(image_path) % 10000}"
        self.current_trajectory = EditingTrajectory(
            trajectory_id=trajectory_id,
            image_path=image_path
        )
        logger.info(f"Started trajectory collection: {trajectory_id}")
        return trajectory_id
    
    def record_step(self, instruction: EditingInstruction, result: OperationResult,
                   score_before: float, score_after: float) -> None:
        """记录编辑步骤"""
        if not self.current_trajectory:
            logger.warning("No active trajectory to record step")
            return
        
        improvement = score_after - score_before
        step = TrajectoryStep(
            step_id=instruction.step_id or f"step_{len(self.current_trajectory.steps)}",
            instruction=instruction,
            result=result,
            timestamp=time.time(),
            score_before=score_before,
            score_after=score_after,
            improvement=improvement
        )
        
        self.current_trajectory.add_step(step)
        logger.debug(f"Recorded step {step.step_id}: improvement={improvement:.3f}")
    
    def complete_trajectory(self, status: TrajectoryStatus) -> Optional[EditingTrajectory]:
        """完成当前轨迹"""
        if not self.current_trajectory:
            return None
        
        self.current_trajectory.complete(status)
        self.trajectory_history.append(self.current_trajectory)
        
        # 保存轨迹到文件
        self._save_trajectory(self.current_trajectory)
        
        completed = self.current_trajectory
        self.current_trajectory = None
        
        logger.info(f"Completed trajectory {completed.trajectory_id}: "
                   f"status={status.value}, improvement={completed.total_improvement:.3f}")
        
        return completed
    
    def _save_trajectory(self, trajectory: EditingTrajectory) -> None:
        """保存轨迹到文件"""
        try:
            file_path = self.storage_path / f"{trajectory.trajectory_id}.json"
            trajectory_data = {
                'trajectory_id': trajectory.trajectory_id,
                'image_path': trajectory.image_path,
                'status': trajectory.status.value,
                'total_improvement': trajectory.total_improvement,
                'reflection_points': trajectory.reflection_points,
                'created_at': trajectory.created_at,
                'completed_at': trajectory.completed_at,
                'steps': [
                    {
                        'step_id': step.step_id,
                        'instruction': step.instruction.instruction,
                        'priority': step.instruction.priority.value,
                        'success': step.result.success,
                        'score_before': step.score_before,
                        'score_after': step.score_after,
                        'improvement': step.improvement,
                        'reflection_triggered': step.reflection_triggered,
                        'reflection_analysis': step.reflection_analysis,
                        'timestamp': step.timestamp
                    }
                    for step in trajectory.steps
                ]
            }
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(trajectory_data, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            logger.error(f"Failed to save trajectory {trajectory.trajectory_id}: {e}")
    
    def load_trajectory(self, trajectory_id: str) -> Optional[EditingTrajectory]:
        """从文件加载轨迹"""
        try:
            file_path = self.storage_path / f"{trajectory_id}.json"
            if not file_path.exists():
                return None
            
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            # 重构轨迹对象
            trajectory = EditingTrajectory(
                trajectory_id=data['trajectory_id'],
                image_path=data['image_path'],
                status=TrajectoryStatus(data['status']),
                total_improvement=data['total_improvement'],
                reflection_points=data['reflection_points'],
                created_at=data['created_at'],
                completed_at=data.get('completed_at')
            )
            
            # 重构步骤
            for step_data in data['steps']:
                instruction = EditingInstruction(
                    instruction=step_data['instruction'],
                    priority=Priority(step_data['priority']),
                    step_id=step_data['step_id']
                )
                
                result = OperationResult(
                    success=step_data['success'],
                    instruction=step_data['instruction'],
                    improvement=step_data['improvement']
                )
                
                step = TrajectoryStep(
                    step_id=step_data['step_id'],
                    instruction=instruction,
                    result=result,
                    timestamp=step_data['timestamp'],
                    score_before=step_data['score_before'],
                    score_after=step_data['score_after'],
                    improvement=step_data['improvement'],
                    reflection_triggered=step_data['reflection_triggered'],
                    reflection_analysis=step_data.get('reflection_analysis')
                )
                
                trajectory.steps.append(step)
            
            return trajectory
            
        except Exception as e:
            logger.error(f"Failed to load trajectory {trajectory_id}: {e}")
            return None
    
    def get_trajectory_statistics(self) -> Dict[str, Any]:
        """获取轨迹统计信息"""
        if not self.trajectory_history:
            return {}
        
        total_trajectories = len(self.trajectory_history)
        successful_trajectories = sum(1 for t in self.trajectory_history 
                                    if t.status == TrajectoryStatus.SUCCESS)
        
        total_steps = sum(len(t.steps) for t in self.trajectory_history)
        successful_steps = sum(sum(1 for s in t.steps if s.is_success) 
                             for t in self.trajectory_history)
        
        avg_improvement = sum(t.total_improvement for t in self.trajectory_history) / total_trajectories
        
        return {
            'total_trajectories': total_trajectories,
            'successful_trajectories': successful_trajectories,
            'success_rate': successful_trajectories / total_trajectories,
            'total_steps': total_steps,
            'successful_steps': successful_steps,
            'step_success_rate': successful_steps / total_steps if total_steps > 0 else 0,
            'average_improvement': avg_improvement,
            'reflection_points': sum(len(t.reflection_points) for t in self.trajectory_history)
        }


# Agent-R 轨迹修正功能
@dataclass
class RevisionCandidate:
    """修正候选"""
    error_step_index: int
    success_segment: List[TrajectoryStep]
    confidence: float
    revision_type: str = "replacement"


class TrajectoryReviser:
    """轨迹修正器 - Agent-R 核心修正机制"""

    def __init__(self):
        """初始化轨迹修正器"""
        self.revision_history: List[EditingTrajectory] = []

    def create_revision_trajectory(self,
                                 failure_trajectory: EditingTrajectory,
                                 success_trajectories: List[EditingTrajectory]) -> Optional[EditingTrajectory]:
        """创建修正轨迹 - Agent-R 核心功能"""

        if failure_trajectory.first_error_step is None:
            logger.warning("No error step identified in failure trajectory")
            return None

        if not success_trajectories:
            logger.warning("No success trajectories available for revision")
            return None

        try:
            # 1. 识别错误步骤
            error_step_index = failure_trajectory.first_error_step
            error_step = failure_trajectory.steps[error_step_index]

            logger.info(f"Revising trajectory at error step {error_step_index}: {error_step.action}")

            # 2. 寻找合适的成功片段
            best_candidate = self._find_best_revision_candidate(
                failure_trajectory, error_step_index, success_trajectories
            )

            if not best_candidate:
                logger.warning("No suitable revision candidate found")
                return None

            # 3. 构建修正轨迹
            revision_trajectory = self._construct_revision_trajectory(
                failure_trajectory, best_candidate
            )

            # 4. 验证修正轨迹
            if self._validate_revision(revision_trajectory):
                self.revision_history.append(revision_trajectory)
                logger.info(f"Successfully created revision trajectory: {revision_trajectory.trajectory_id}")
                return revision_trajectory
            else:
                logger.warning("Revision trajectory validation failed")
                return None

        except Exception as e:
            logger.error(f"Failed to create revision trajectory: {e}")
            return None

    def _find_best_revision_candidate(self,
                                    failure_trajectory: EditingTrajectory,
                                    error_step_index: int,
                                    success_trajectories: List[EditingTrajectory]) -> Optional[RevisionCandidate]:
        """寻找最佳修正候选"""

        candidates = []

        for success_traj in success_trajectories:
            # 寻找与错误步骤相似的成功片段
            for start_idx in range(len(success_traj.steps)):
                # 提取成功片段
                success_segment = success_traj.steps[start_idx:]

                if not success_segment:
                    continue

                # 计算相似度和置信度
                confidence = self._calculate_revision_confidence(
                    failure_trajectory.steps[:error_step_index],
                    success_segment
                )

                if confidence > 0.3:  # 置信度阈值
                    candidate = RevisionCandidate(
                        error_step_index=error_step_index,
                        success_segment=success_segment,
                        confidence=confidence,
                        revision_type="replacement"
                    )
                    candidates.append(candidate)

        # 选择置信度最高的候选
        if candidates:
            best_candidate = max(candidates, key=lambda c: c.confidence)
            logger.info(f"Selected revision candidate with confidence {best_candidate.confidence:.3f}")
            return best_candidate

        return None

    def _calculate_revision_confidence(self,
                                     failure_prefix: List[TrajectoryStep],
                                     success_segment: List[TrajectoryStep]) -> float:
        """计算修正置信度"""

        # 简化的置信度计算
        confidence = 0.5  # 基础置信度

        # 基于成功片段的奖励
        if success_segment:
            avg_reward = sum(step.reward for step in success_segment) / len(success_segment)
            confidence += min(0.3, max(-0.3, avg_reward))

        # 基于长度匹配
        if len(success_segment) >= 2:  # 至少有2步的成功片段
            confidence += 0.1

        # 基于动作多样性
        actions = set(step.action for step in success_segment)
        if len(actions) > 1:
            confidence += 0.1

        return max(0.0, min(1.0, confidence))

    def _construct_revision_trajectory(self,
                                     failure_trajectory: EditingTrajectory,
                                     candidate: RevisionCandidate) -> EditingTrajectory:
        """构建修正轨迹"""

        revision_id = f"revision_{failure_trajectory.trajectory_id}_{candidate.error_step_index}"

        revision_trajectory = EditingTrajectory(
            trajectory_id=revision_id,
            image_path=failure_trajectory.image_path,
            trajectory_type="revision"
        )

        # 1. 保留错误步骤之前的部分
        for i in range(candidate.error_step_index):
            step = failure_trajectory.steps[i]
            revision_trajectory.add_step(
                action=step.action,
                observation=step.observation,
                reward=step.reward,
                parent_id=step.parent_id
            )

        # 2. 添加成功片段
        for step in candidate.success_segment:
            revision_trajectory.add_step(
                action=step.action,
                observation=step.observation,
                reward=step.reward,
                parent_id=step.parent_id
            )

        logger.info(f"Constructed revision trajectory with {len(revision_trajectory.steps)} steps")
        return revision_trajectory

    def _validate_revision(self, revision_trajectory: EditingTrajectory) -> bool:
        """验证修正轨迹的有效性"""

        # 基本验证
        if not revision_trajectory.steps:
            return False

        # 检查总奖励是否改善
        if revision_trajectory.total_reward <= 0:
            logger.warning(f"Revision trajectory has non-positive reward: {revision_trajectory.total_reward}")
            return False

        # 检查步骤连续性
        for i, step in enumerate(revision_trajectory.steps):
            if not step.action or step.action.strip() == "":
                logger.warning(f"Empty action at step {i}")
                return False

        return True

    def batch_create_revisions(self,
                             failure_trajectories: List[EditingTrajectory],
                             success_trajectories: List[EditingTrajectory]) -> List[EditingTrajectory]:
        """批量创建修正轨迹"""

        revisions = []

        for failure_traj in failure_trajectories:
            revision = self.create_revision_trajectory(failure_traj, success_trajectories)
            if revision:
                revisions.append(revision)

        logger.info(f"Created {len(revisions)} revision trajectories from {len(failure_trajectories)} failures")
        return revisions

    def get_revision_statistics(self) -> Dict[str, Any]:
        """获取修正统计信息"""

        if not self.revision_history:
            return {}

        total_revisions = len(self.revision_history)
        avg_reward = sum(traj.total_reward for traj in self.revision_history) / total_revisions
        avg_length = sum(len(traj.steps) for traj in self.revision_history) / total_revisions

        return {
            'total_revisions': total_revisions,
            'average_reward': avg_reward,
            'average_length': avg_length,
            'revision_types': [traj.trajectory_type for traj in self.revision_history]
        }


class WeightedVoting:
    """加权投票机制 - Agent-R 测试阶段的输出选择"""

    def __init__(self):
        """初始化加权投票器"""
        self.weight_config = {
            'reward_weight': 0.4,      # 奖励权重
            'length_weight': 0.2,      # 长度权重（更短更好）
            'error_weight': 0.3,       # 错误数量权重（更少更好）
            'type_weight': 0.1         # 轨迹类型权重
        }

    def select_best_trajectory(self, trajectories: List[EditingTrajectory]) -> Optional[EditingTrajectory]:
        """使用加权投票选择最佳轨迹"""

        if not trajectories:
            return None

        if len(trajectories) == 1:
            return trajectories[0]

        # 计算每个轨迹的权重分数
        trajectory_scores = []

        for traj in trajectories:
            score = self._calculate_trajectory_score(traj, trajectories)
            trajectory_scores.append((traj, score))

        # 选择分数最高的轨迹
        best_trajectory, best_score = max(trajectory_scores, key=lambda x: x[1])

        logger.info(f"Selected trajectory {best_trajectory.trajectory_id} with score {best_score:.3f}")
        return best_trajectory

    def _calculate_trajectory_score(self,
                                  trajectory: EditingTrajectory,
                                  all_trajectories: List[EditingTrajectory]) -> float:
        """计算轨迹的加权分数"""

        score = 0.0

        # 1. 奖励分数（归一化）
        max_reward = max(traj.total_reward for traj in all_trajectories)
        min_reward = min(traj.total_reward for traj in all_trajectories)

        if max_reward > min_reward:
            reward_score = (trajectory.total_reward - min_reward) / (max_reward - min_reward)
        else:
            reward_score = 0.5

        score += reward_score * self.weight_config['reward_weight']

        # 2. 长度分数（更短更好）
        max_length = max(len(traj.steps) for traj in all_trajectories)
        min_length = min(len(traj.steps) for traj in all_trajectories)

        if max_length > min_length:
            length_score = 1.0 - (len(trajectory.steps) - min_length) / (max_length - min_length)
        else:
            length_score = 0.5

        score += length_score * self.weight_config['length_weight']

        # 3. 错误数量分数
        error_count = sum(1 for step in trajectory.steps if step.is_error)
        max_errors = max(sum(1 for step in traj.steps if step.is_error) for traj in all_trajectories)

        if max_errors > 0:
            error_score = 1.0 - (error_count / max_errors)
        else:
            error_score = 1.0

        score += error_score * self.weight_config['error_weight']

        # 4. 轨迹类型分数
        type_scores = {
            'success': 1.0,
            'revision': 0.8,
            'rollout': 0.6,
            'failure': 0.2,
            'neutral': 0.5
        }

        type_score = type_scores.get(trajectory.trajectory_type, 0.5)
        score += type_score * self.weight_config['type_weight']

        return score
