"""
Agent-R Controller
Agent-R 主控制器

Simplified implementation of Agent-R training and inference pipeline
简化的 Agent-R 训练和推理流水线实现
"""

import logging
import time
from typing import List, Optional, Dict, Any, Tuple
from PIL import Image

from .mcts_search import SimplifiedMCTS, create_simple_evaluator
from .trajectory import EditingTrajectory, TrajectoryStatus, TrajectoryReviser, WeightedVoting
from .reflection import ReflectionEngine
from .planning import UnifiedLLMClient
from ..core.evaluator import AestheticEvaluator

logger = logging.getLogger(__name__)


class AgentRController:
    """Agent-R 控制器 - 实现完整的 Agent-R 流程"""
    
    def __init__(self, llm_client: UnifiedLLMClient, aesthetic_evaluator: AestheticEvaluator):
        """初始化 Agent-R 控制器"""
        self.llm_client = llm_client
        self.aesthetic_evaluator = aesthetic_evaluator
        
        # 初始化核心组件
        self.mcts_searcher = SimplifiedMCTS(max_iterations=30, max_depth=4)
        self.trajectory_reviser = TrajectoryReviser()  # 简化的初始化
        self.reflection_engine = ReflectionEngine(llm_client)
        self.weighted_voting = WeightedVoting()
        
        # 创建评估函数
        self.evaluator_func = create_simple_evaluator(aesthetic_evaluator)
        
        # 统计信息
        self.stats = {
            'total_rollouts': 0,
            'successful_trajectories': 0,
            'failed_trajectories': 0,
            'revision_trajectories': 0,
            'processing_time': 0.0
        }
    
    def process_image_agent_r(self, image: Image.Image, instruction: str = None) -> Dict[str, Any]:
        """使用 Agent-R 方法处理图像"""
        start_time = time.time()
        
        try:
            logger.info("Starting Agent-R image processing")
            
            # Phase 1: MCTS 轨迹生成
            trajectories = self._generate_trajectories_with_mcts(image)
            
            # Phase 2: 轨迹分类和修正
            classified_trajectories = self._classify_and_revise_trajectories(trajectories)
            
            # Phase 3: 加权投票选择最佳结果
            best_trajectory = self._select_best_trajectory(classified_trajectories)
            
            # 更新统计信息
            processing_time = time.time() - start_time
            self.stats['processing_time'] += processing_time
            
            result = {
                'success': best_trajectory is not None,
                'best_trajectory': best_trajectory,
                'all_trajectories': classified_trajectories,
                'processing_time': processing_time,
                'stats': self.stats.copy()
            }
            
            logger.info(f"Agent-R processing completed in {processing_time:.2f}s")
            return result
            
        except Exception as e:
            logger.error(f"Agent-R processing failed: {e}")
            return {
                'success': False,
                'error': str(e),
                'processing_time': time.time() - start_time
            }
    
    def _generate_trajectories_with_mcts(self, image: Image.Image) -> List[EditingTrajectory]:
        """Phase 1: 使用 MCTS 生成多条轨迹"""
        logger.info("Phase 1: Generating trajectories with MCTS")
        
        try:
            # 执行 MCTS 搜索
            trajectories = self.mcts_searcher.search(image, self.evaluator_func)
            
            self.stats['total_rollouts'] += len(trajectories)
            
            logger.info(f"Generated {len(trajectories)} trajectories via MCTS")
            return trajectories
            
        except Exception as e:
            logger.error(f"MCTS trajectory generation failed: {e}")
            return []
    
    def _classify_and_revise_trajectories(self, trajectories: List[EditingTrajectory]) -> Dict[str, List[EditingTrajectory]]:
        """Phase 2: 分类轨迹并创建修正轨迹"""
        logger.info("Phase 2: Classifying and revising trajectories")
        
        classified = {
            'success': [],
            'failure': [],
            'revision': [],
            'neutral': []
        }
        
        # 分类轨迹
        for traj in trajectories:
            if traj.total_reward > 0.1:
                traj.trajectory_type = "success"
                classified['success'].append(traj)
                self.stats['successful_trajectories'] += 1
            elif traj.total_reward < -0.1:
                traj.trajectory_type = "failure"
                classified['failure'].append(traj)
                self.stats['failed_trajectories'] += 1
            else:
                traj.trajectory_type = "neutral"
                classified['neutral'].append(traj)
        
        # 识别失败轨迹中的错误步骤
        for failure_traj in classified['failure']:
            error_step_idx = self.reflection_engine.identify_first_error(failure_traj)
            if error_step_idx is not None:
                failure_traj.first_error_step = error_step_idx
        
        # 创建修正轨迹
        if classified['failure'] and classified['success']:
            revision_trajectories = self.trajectory_reviser.batch_create_revisions(
                classified['failure'], classified['success']
            )
            classified['revision'] = revision_trajectories
            self.stats['revision_trajectories'] += len(revision_trajectories)
        
        logger.info(f"Classified trajectories: {len(classified['success'])} success, "
                   f"{len(classified['failure'])} failure, {len(classified['revision'])} revision")
        
        return classified
    
    def _select_best_trajectory(self, classified_trajectories: Dict[str, List[EditingTrajectory]]) -> Optional[EditingTrajectory]:
        """Phase 3: 使用加权投票选择最佳轨迹"""
        logger.info("Phase 3: Selecting best trajectory with weighted voting")
        
        # 收集所有候选轨迹
        candidates = []
        
        # 优先考虑修正轨迹和成功轨迹
        candidates.extend(classified_trajectories.get('revision', []))
        candidates.extend(classified_trajectories.get('success', []))
        
        # 如果没有好的候选，考虑中性轨迹
        if not candidates:
            candidates.extend(classified_trajectories.get('neutral', []))
        
        # 最后考虑失败轨迹（如果没有其他选择）
        if not candidates:
            candidates.extend(classified_trajectories.get('failure', []))
        
        if not candidates:
            logger.warning("No candidate trajectories available")
            return None
        
        # 使用加权投票选择最佳轨迹
        best_trajectory = self.weighted_voting.select_best_trajectory(candidates)
        
        if best_trajectory:
            logger.info(f"Selected best trajectory: {best_trajectory.trajectory_id} "
                       f"(type: {best_trajectory.trajectory_type}, reward: {best_trajectory.total_reward:.3f})")
        
        return best_trajectory
    
    def batch_process_with_agent_r(self, images: List[Image.Image]) -> Dict[str, Any]:
        """批量处理图像使用 Agent-R"""
        batch_start_time = time.time()
        results = []
        
        logger.info(f"Starting batch processing of {len(images)} images with Agent-R")
        
        for i, image in enumerate(images, 1):
            logger.info(f"Processing image {i}/{len(images)}")
            result = self.process_image_agent_r(image)
            results.append(result)
        
        # 批次统计
        successful_count = sum(1 for r in results if r.get('success', False))
        total_trajectories = sum(len(r.get('all_trajectories', {}).get('success', [])) + 
                               len(r.get('all_trajectories', {}).get('failure', [])) + 
                               len(r.get('all_trajectories', {}).get('revision', []))
                               for r in results)
        
        batch_summary = {
            'total_images': len(images),
            'successful_images': successful_count,
            'success_rate': successful_count / len(images) if images else 0,
            'total_trajectories_generated': total_trajectories,
            'average_trajectories_per_image': total_trajectories / len(images) if images else 0,
            'batch_processing_time': time.time() - batch_start_time,
            'individual_results': results,
            'overall_stats': self.stats.copy()
        }
        
        logger.info(f"Batch processing completed: {successful_count}/{len(images)} successful")
        return batch_summary
    
    def get_training_data(self) -> Dict[str, List[EditingTrajectory]]:
        """获取用于训练的轨迹数据"""
        training_data = {
            'good_trajectories': [],
            'revision_trajectories': []
        }
        
        # 从修正器历史中获取数据
        revision_history = self.trajectory_reviser.revision_history
        training_data['revision_trajectories'] = revision_history
        
        logger.info(f"Collected training data: {len(revision_history)} revision trajectories")
        return training_data
    
    def get_system_statistics(self) -> Dict[str, Any]:
        """获取系统统计信息"""
        base_stats = self.stats.copy()
        
        # 添加组件统计
        base_stats.update({
            'revision_stats': self.trajectory_reviser.get_revision_statistics()
        })
        
        return base_stats
    
    def reset_statistics(self) -> None:
        """重置统计信息"""
        self.stats = {
            'total_rollouts': 0,
            'successful_trajectories': 0,
            'failed_trajectories': 0,
            'revision_trajectories': 0,
            'processing_time': 0.0
        }
        
        # 清空历史记录
        self.trajectory_reviser.revision_history.clear()
        self.reflection_engine.error_patterns.clear()
        
        logger.info("System statistics reset")


def create_agent_r_controller(llm_client: UnifiedLLMClient, 
                             aesthetic_evaluator: AestheticEvaluator) -> AgentRController:
    """创建 Agent-R 控制器的工厂函数"""
    return AgentRController(llm_client, aesthetic_evaluator)
