# 导入PyTorch深度学习框架，用于模型计算
import torch
# 导入NumPy数值计算库，用于数组操作
import numpy as np
# 导入类型提示模块，用于代码类型标注
from typing import Dict, List, Any, Optional, Union, Tuple
# 导入PIL图像处理库，用于图像操作
from PIL import Image
# 导入日志模块，用于记录运行信息
import logging
# 导入JSON模块，用于JSON数据处理
import json
# 导入base64编码模块，用于图像编码
import base64
# 导入io模块，用于内存中的字节流操作
import io
# 导入dataclass装饰器，用于创建数据类
from dataclasses import dataclass
# 导入时间模块，用于时间相关操作
import time
# 导入正则表达式模块，用于文本解析
import re

# 导入统一架构组件
# 移除 BaseAgent 依赖，使用简单的实现
from .unified_types import PerceptionResult, ImageInfo, ImageScore  # 导入统一的类型定义

# 导入美学评估器和配置文件类
from ..core.evaluator import AestheticEvaluator  # 导入美学评估器
from ..core.profile import Profile  # 导入配置文件类

# 创建日志记录器实例，用于记录模块运行日志
logger = logging.getLogger(__name__)


# 删除重复的 PerceptionResult 定义，使用统一类型
# from .unified_types import PerceptionResult


class VisionLanguageModel:
    """Base class for Vision-Language Models
    视觉语言模型基类，为所有VLM模型提供统一接口
    """
    
    def __init__(self, model_name: str, api_key: Optional[str] = None):
        """初始化视觉语言模型
        
        Args:
            model_name: 模型名称标识符
            api_key: API密钥（可选，本地模型不需要）
        """
        self.model_name = model_name  # 存储模型名称
        self.api_key = api_key  # 存储API密钥
    
    def analyze_image(self, image: Image.Image, prompt: str) -> str:
        """分析图像并返回文本描述
        
        Args:
            image: PIL图像对象
            prompt: 分析提示词
            
        Returns:
            str: 分析结果文本
        """
        try:
            # 尝试执行具体的分析实现
            return self._perform_analysis(image, prompt)
        except Exception as e:
            # 记录错误日志并返回错误信息
            logger.error(f"{self.model_name} analysis failed: {str(e)}")
            return f"Analysis failed: {str(e)}"
    
    def _perform_analysis(self, image: Image.Image, prompt: str) -> str:
        """执行具体的图像分析（子类需要实现）
        
        Args:
            image: PIL图像对象
            prompt: 分析提示词
            
        Returns:
            str: 分析结果文本
        """
        # 抛出未实现错误，强制子类重写此方法
        raise NotImplementedError
    
    def _prepare_image(self, image: Image.Image, max_size_mb: int = 5) -> str:
        """将图像转换为base64编码并调整大小以满足API限制
        
        Args:
            image: PIL图像对象
            max_size_mb: 最大文件大小限制（MB）
            
        Returns:
            str: base64编码的图像字符串
        """
        # 创建内存字节流缓冲区
        buffered = io.BytesIO()
        # 将图像保存为JPEG格式到内存缓冲区，质量85%
        image.save(buffered, format="JPEG", quality=85)
        # 将字节数据编码为base64字符串
        img_str = base64.b64encode(buffered.getvalue()).decode()
        
        # 检查图像大小是否超过限制，如果超过则调整大小
        if len(img_str) > max_size_mb * 1024 * 1024:
            # 记录警告日志
            logger.warning(f"Image too large, resizing to fit {max_size_mb}MB limit")
            # 根据当前大小估算缩放因子
            scale_factor = (max_size_mb * 1024 * 1024 / len(img_str)) ** 0.5
            # 计算新的图像尺寸
            new_size = (int(image.width * scale_factor), int(image.height * scale_factor))
            # 使用Lanczos重采样算法调整图像大小
            image = image.resize(new_size, Image.Resampling.LANCZOS)
            
            # 重新创建内存缓冲区并保存调整后的图像
            buffered = io.BytesIO()
            image.save(buffered, format="JPEG", quality=85)
            # 重新编码为base64字符串
            img_str = base64.b64encode(buffered.getvalue()).decode()
            
        return img_str  # 返回处理后的base64图像字符串


class OpenAIVisionModel(VisionLanguageModel):
    """OpenAI GPT-4 Vision model implementation
    OpenAI GPT-4 Vision模型实现类
    """
    
    def __init__(self, api_key: str):
        """初始化OpenAI Vision模型
        
        Args:
            api_key: OpenAI API密钥
        """
        # 调用父类初始化方法，传入模型名称和API密钥
        super().__init__("gpt-4-vision", api_key)
        
    def _perform_analysis(self, image: Image.Image, prompt: str) -> str:
        """使用OpenAI GPT-4 Vision分析图像
        
        Args:
            image: PIL图像对象
            prompt: 分析提示词
            
        Returns:
            str: GPT-4 Vision的分析结果
        """
        # 导入OpenAI库
        import openai
        
        # 准备图像数据（OpenAI最大支持20MB）
        img_str = self._prepare_image(image, max_size_mb=20)
        
        # 创建OpenAI客户端实例
        client = openai.OpenAI(api_key=self.api_key)
        # 调用GPT-4 Vision API进行图像分析
        response = client.chat.completions.create(
            model="gpt-4-vision-preview",  # 指定使用GPT-4 Vision预览版模型
            messages=[
                {
                    "role": "user",  # 用户角色
                    "content": [
                        {"type": "text", "text": prompt},  # 文本提示
                        {
                            "type": "image_url",  # 图像URL类型
                            "image_url": {
                                "url": f"data:image/jpeg;base64,{img_str}"  # base64编码的图像数据URL
                            }
                        }
                    ]
                }
            ],
            max_tokens=1000,  # 最大生成token数量
            timeout=30  # 请求超时时间（秒）
        )
        # 返回模型生成的内容
        return response.choices[0].message.content


class AnthropicVisionModel(VisionLanguageModel):
    """Anthropic Claude Vision model implementation
    Anthropic Claude Vision模型实现类
    """
    
    def __init__(self, api_key: str):
        """初始化Claude Vision模型
        
        Args:
            api_key: Anthropic API密钥
        """
        # 调用父类初始化方法
        super().__init__("claude-vision", api_key)
        
    def _perform_analysis(self, image: Image.Image, prompt: str) -> str:
        """使用Anthropic Claude Vision分析图像
        
        Args:
            image: PIL图像对象
            prompt: 分析提示词
            
        Returns:
            str: Claude Vision的分析结果
        """
        # 导入Anthropic库
        import anthropic
        
        # 准备图像数据（Claude最大支持5MB）
        img_str = self._prepare_image(image, max_size_mb=5)
        
        # 创建Anthropic客户端实例
        client = anthropic.Anthropic(api_key=self.api_key)
        # 调用Claude Vision API进行图像分析
        response = client.messages.create(
            model="claude-3-sonnet-20240229",  # 指定使用Claude 3 Sonnet模型
            max_tokens=1000,  # 最大生成token数量
            messages=[
                {
                    "role": "user",  # 用户角色
                    "content": [
                        {
                            "type": "image",  # 图像类型
                            "source": {
                                "type": "base64",  # base64编码类型
                                "media_type": "image/jpeg",  # 媒体类型为JPEG图像
                                "data": img_str  # base64编码的图像数据
                            }
                        },
                        {
                            "type": "text",  # 文本类型
                            "text": prompt  # 分析提示词
                        }
                    ]
                }
            ],
            timeout=30  # 请求超时时间（秒）
        )
        # 返回模型生成的文本内容
        return response.content[0].text


class LlamaVisionModel(VisionLanguageModel):
    """Llama Vision model implementation (local)
    Llama Vision模型实现类（本地运行）
    """

    # 类级别的模型和处理器缓存，避免重复加载
    _model = None
    _processor = None

    def __init__(self):
        """初始化Llama Vision模型（本地运行，无需API密钥）"""
        # 调用父类初始化方法，不需要API密钥
        super().__init__("llama-vision", None)
        # 加载模型到内存
        self._load_model()
    
    def _load_model(self):
        """从本地加载Llama Vision模型，使用类级别缓存避免重复加载"""
        # 检查类级别缓存中是否已有模型和处理器
        if LlamaVisionModel._model is not None and LlamaVisionModel._processor is not None:
            return  # 如果已加载则直接返回
            
        try:
            # 从transformers库导入Llava模型相关类
            from transformers import LlavaNextProcessor, LlavaNextForConditionalGeneration
            
            # 指定要使用的模型ID
            model_id = "llava-hf/llava-v1.6-mistral-7b-hf"
            logger.info(f"Loading Llama Vision model: {model_id}")  # 记录模型加载日志
            
            # 加载处理器，用于预处理输入数据
            LlamaVisionModel._processor = LlavaNextProcessor.from_pretrained(model_id,use_fast=True)
            # 加载模型，配置为半精度浮点数以节省内存
            LlamaVisionModel._model = LlavaNextForConditionalGeneration.from_pretrained(
                model_id,
                torch_dtype=torch.bfloat16,  # 使用bfloat16数据类型节省内存
                low_cpu_mem_usage=True,  # 低CPU内存使用模式
                device_map="auto" if torch.cuda.is_available() else None,  # 自动设备映射（如果有GPU则使用）
            )
                
            logger.info("Llama Vision model loaded successfully")  # 记录模型加载成功日志
            
        except Exception as e:
            # 模型加载失败时记录错误并重置缓存
            logger.error(f"Failed to load Llama Vision model: {e}")
            LlamaVisionModel._model = None
            LlamaVisionModel._processor = None
    
    def _perform_analysis(self, image: Image.Image, prompt: str) -> str:
        """使用本地Llama Vision模型分析图像
        
        Args:
            image: PIL图像对象
            prompt: 分析提示词
            
        Returns:
            str: Llama Vision的分析结果
        """
        # 检查模型是否可用
        if LlamaVisionModel._model is None or LlamaVisionModel._processor is None:
            return "Model not available"  # 模型不可用时返回错误信息
        
        # 准备对话格式的输入数据
        conversation = [
            {
                "role": "user",  # 用户角色
                "content": [
                    {"type": "text", "text": prompt},  # 文本提示
                    {"type": "image"},  # 图像输入
                ],
            },
        ]
        
        # 应用聊天模板格式化输入
        prompt_text = LlamaVisionModel._processor.apply_chat_template(
            conversation, add_generation_prompt=True  # 添加生成提示
        )
        
        # 使用处理器编码图像和文本输入
        inputs = LlamaVisionModel._processor(
            image, prompt_text, return_tensors="pt"  # 返回PyTorch张量格式
        ).to(LlamaVisionModel._model.device)  # 移动到模型所在设备
        
        # 在不计算梯度的上下文中生成响应
        with torch.no_grad():
            output = LlamaVisionModel._model.generate(
                **inputs,  # 解包输入参数
                max_new_tokens=2048,  # 最大新生成token数量
                do_sample=False  # 不使用采样，确定性生成
            )
        
        # 解码生成的响应文本
        return LlamaVisionModel._processor.decode(
            output[0][len(inputs["input_ids"][0]):],  # 只解码新生成的部分
            skip_special_tokens=True  # 跳过特殊token
        ).strip()  # 去除首尾空白字符


class PerceptionAgent:
    """
    统一架构的感知代理，负责图像内容分析和构图问题识别
    Unified perception agent for image content analysis and composition issue identification
    """

    def __init__(self, profile: Profile):
        """
        初始化感知代理

        Args:
            profile: 系统配置对象，包含模型配置和API密钥等信息
        """
        self.profile = profile  # 存储配置文件对象
        self.vision_model = None  # 初始化视觉模型为空
        self.logger = logging.getLogger(__name__)  # 初始化日志记录器

        # 初始化组件
        self._initialize_vision_model()
        self.logger.info("PerceptionAgent initialized successfully")

    def _initialize_vision_model(self):
        """初始化视觉语言模型"""
    
    def _initialize_vision_model(self):
        """初始化视觉语言模型，根据配置选择合适的模型"""
        # 检查配置文件是否存在
        if not self.profile.profile:
            logger.warning("No profile configuration found, skipping vision model initialization")
            return

        # 从配置中获取指定的感知模型名称
        model_name = self.profile.profile.perception_model
        
        # 使用字典映射简化模型创建逻辑，每个模型对应一个创建函数
        vision_models = {
            "gpt-4-vision": lambda: OpenAIVisionModel(self.profile.api.openai_api_key) 
                            if self.profile.api and self.profile.api.openai_api_key else None,  # 创建OpenAI模型（需要API密钥）
            "claude-vision": lambda: AnthropicVisionModel(self.profile.api.anthropic_api_key) 
                             if self.profile.api and self.profile.api.anthropic_api_key else None,  # 创建Claude模型（需要API密钥）
            "llama-vision": lambda: LlamaVisionModel()  # 创建Llama模型（本地运行）
        }
        
        try:
            # 根据模型名称获取对应的创建函数
            model_creator = vision_models.get(model_name)
            if model_creator:
                # 执行模型创建函数
                self.vision_model = model_creator()
                if self.vision_model:
                    logger.info(f"{model_name} initialized successfully")  # 记录初始化成功日志
                else:
                    logger.warning(f"Failed to initialize {model_name}, API key may be missing")  # API密钥可能缺失
            else:
                logger.warning(f"Unknown vision model: {model_name}, falling back to basic analysis")  # 未知模型类型
        except Exception as e:
            # 初始化失败时记录错误并设置模型为空
            logger.error(f"Failed to initialize vision model {model_name}: {e}")
            self.vision_model = None
    
    def analyze_image(self, image: Union[Image.Image, str]) -> PerceptionResult:
        """
        分析图像内容和构图问题

        Args:
            image: PIL Image对象或图像文件路径

        Returns:
            PerceptionResult: 包含完整分析结果的对象
        """
        def _perform_analysis():
            """执行实际的图像分析逻辑"""
            # 根据输入类型加载图像
            if isinstance(image, str):
                # 如果是文件路径，则打开图像文件并转换为RGB模式
                loaded_image = Image.open(image).convert("RGB")
            else:
                # 如果是PIL Image对象，则创建副本
                loaded_image = image.copy()

            # 从图像创建ImageInfo对象，包含图像的基本信息
            image_info = ImageInfo.from_image(loaded_image)

            # 尝试使用视觉语言模型进行分析
            if self.vision_model:
                try:
                    # 创建分析提示词
                    prompt = self._create_analysis_prompt()
                    # 使用VLM模型分析图像
                    response = self.vision_model.analyze_image(loaded_image, prompt)
                    # 解析VLM的响应结果
                    content_description, composition_issues, recommendations = self._parse_vlm_response(response)
                    # 记录使用的模型名称
                    model_used = getattr(self.profile.profile, 'perception_model', 'unknown') if self.profile.profile else 'unknown'

                except Exception as e:
                    # VLM分析失败时记录错误并使用备用方案
                    self.logger.error(f"VLM analysis failed: {e}")
                    content_description, composition_issues, recommendations = self._fallback_analysis(loaded_image)
                    model_used = "fallback"
            else:
                # 没有VLM模型时使用备用分析方案
                content_description, composition_issues, recommendations = self._fallback_analysis(loaded_image)
                model_used = "fallback"

            # 创建分析结果对象
            result = PerceptionResult(
                success=True,  # 标记分析成功
                message="Image analysis completed",  # 分析完成消息
                content_description=content_description,  # 图像内容描述
                composition_issues=composition_issues,  # 构图问题列表
                recommendations=recommendations,  # 改进建议列表
                image_info=image_info  # 图像基本信息
            )

            return result

        # 直接执行分析函数
        try:
            return _perform_analysis()
        except Exception as e:
            self.logger.error(f"Image analysis failed: {e}")
            # 返回失败结果
            return PerceptionResult(
                success=False,
                message=f"Analysis failed: {str(e)}",
                content_description="",
                composition_issues=[],
                recommendations=[]
            )
    
    
    def _create_analysis_prompt(self) -> str:
        """创建用于VLM分析图像构图和美学质量的提示词
        
        Returns:
            str: 详细的分析提示词，指导模型进行专业的摄影构图分析
        """
        return """
            Please detailed analyze the following image with a focus on photographic composition and aesthetic quality. Your analysis must be returned in the following strict JSON format:

            {
            "description": "A detailed description of all visible visual elements in the image, including subject, background, lighting, spatial layout, color palette, and any significant visual structures (e.g., symmetry, depth, leading lines)."
            "composition_issues": [
                "List all visible and concrete issues related to the image's composition. Be specific and technical.",
                "Each issue must refer to at least one recognized principle of photographic composition (see list below).",
                "Use precise and photographic terminology such as 'imbalance', 'misalignment', 'visual clutter', 'weak focal point', 'flat depth', etc.",
                "If applicable, include spatial anchors (e.g., 'top-left corner', 'bottom-right quadrant') to localize the issue.",
                "Avoid generic phrases like 'not ideal' or 'a bit off'. Be detailed and direct.",
                "Specify if the image appears tilted or misaligned. If correction is needed, state direction and estimated degree (e.g., 'Rotate clockwise ~5°')."
                "List at least 3 distinct composition issues if present."
            ],
            "recommendations": [
                Each recommendation must map to a real image-editing operation: crop, rotate, blur, desaturate, brighten, clone/remove object, change contrast, etc.
            ],


            Evaluate the image based on the following compositional principles:

            - Rule of Thirds: Is the subject aligned with the intersections or lines of a 3×3 grid?
            - Visual Balance: Are elements symmetrically or asymmetrically balanced in a pleasing way?
            - Leading Lines: Are natural lines (e.g., roads, rails, architecture) effectively guiding the viewer's gaze?
            - Framing and Cropping: Are edges clean? Is the subject well-contained and intentionally placed?
            - Subject Placement and Focal Points: Is the subject clear, dominant, and well-positioned?
            - Color Harmony and Contrast: Do colors enhance focus and mood without clashing?
            - Depth and Layering: Does the image feel spatially deep, with foreground, midground, and background?
            - Perspective and Alignment: Are verticals/horizontals straight if appropriate? Is the viewpoint intentional?
            - Overall Visual Appeal: Does the image evoke visual or emotional interest through its composition?

            Additional Instructions:
            - Your output **must strictly follow** the above JSON schema and field names.
            - Use precise, photographic language appropriate for technical critique.
            - Be **specific** in all observations. Avoid vague phrases like "could be better".
            - Focus only on elements **visible in the image**, do not assume or imagine outside content.
            - For `"recommendations"`: Each should map clearly to a real photo editing action (e.g., crop left edge, darken background, desaturate sky).
            - For `"notable_distractions"`: Be concrete. Mention position (e.g., "a pedestrian in lower-left corner") if possible.

                """

    def _parse_vlm_response(self, response: str) -> Tuple[str, List[str], List[str]]:
        """解析VLM响应以提取结构化信息
        
        Args:
            response: VLM模型返回的原始文本响应
            
        Returns:
            Tuple[str, List[str], List[str]]: 包含内容描述、构图问题和建议的元组
        """
        # 设置默认返回值
        content_description = "No description available"  # 默认内容描述
        composition_issues = []  # 默认构图问题列表
        recommendations = []  # 默认建议列表
        
        # 尝试提取并解析JSON格式的响应
        try:
            # 使用正则表达式查找JSON部分（包含换行符的多行匹配）
            json_match = re.search(r'({.*})', response, re.DOTALL)
            if json_match:
                # 解析JSON数据
                data = json.loads(json_match.group(1))
                # 提取各个字段，如果不存在则使用默认值
                content_description = data.get("description", content_description)
                composition_issues = data.get("composition_issues", [])
                recommendations = data.get("recommendations", [])
                return content_description, composition_issues, recommendations
        except Exception:
            # JSON解析失败时记录警告并继续使用文本解析
            logger.warning("Failed to parse VLM response as JSON")
        
        # 文本解析方式：创建三个主要部分的存储字典
        sections = {"description": [], "issues": [], "recommendations": []}
        current_section = None  # 当前正在处理的部分
        
        # 逐行解析响应文本
        for line in response.split('\n'):
            line = line.strip()  # 去除行首尾空白字符
            if not line:
                continue  # 跳过空行
            
            # 根据关键词识别当前处理的部分
            if re.search(r'(description|content|what\'s\s+in)', line.lower()):
                current_section = "description"  # 识别为描述部分
            elif re.search(r'(issue|problem|concern)', line.lower()):
                current_section = "issues"  # 识别为问题部分
            elif re.search(r'(recommend|suggest|improve)', line.lower()):
                current_section = "recommendations"  # 识别为建议部分
            
            # 提取列表项（以破折号、圆点、星号或数字开头的行）
            if re.match(r'^[-•*\d]+\.?\s+', line) and current_section in sections:
                # 去除列表标记，提取实际内容
                item = re.sub(r'^[-•*\d]+\.?\s+', '', line)
                if item:
                    sections[current_section].append(item)
            elif current_section == "description" and len(line) > 10:
                # 对于描述部分，添加足够长的文本行
                sections["description"].append(line)
        
        # 处理收集到的信息
        if sections["description"]:
            content_description = sections["description"][0]  # 使用第一个描述作为主要描述
        composition_issues = sections["issues"]  # 构图问题列表
        recommendations = sections["recommendations"]  # 建议列表
        
        return content_description, composition_issues, recommendations
    
    def _fallback_analysis(self, image: Image.Image) -> Tuple[str, List[str], List[str]]:
        """当VLM不可用时的基础回退分析方案
        
        Args:
            image: PIL图像对象
            
        Returns:
            Tuple[str, List[str], List[str]]: 基础分析结果
        """
        # 获取图像尺寸
        width, height = image.size
        # 计算宽高比
        aspect_ratio = width / height
        
        # 创建基础的内容描述
        content_description = f"Image with dimensions {width}x{height} (aspect ratio {aspect_ratio:.2f})"
        
        # 初始化问题和建议列表
        composition_issues = []
        recommendations = []
        
        # 基于宽高比的基础构图分析
        if aspect_ratio < 0.8:
            # 过于狭长的纵向图像
            composition_issues.append("Very tall aspect ratio may create imbalanced composition")
            recommendations.append("Consider cropping to a more balanced aspect ratio")
        elif aspect_ratio > 2.0:
            # 过于宽广的横向图像
            composition_issues.append("Very wide aspect ratio may create imbalanced composition")
            recommendations.append("Consider cropping to a more balanced aspect ratio")
        
        # 基于分辨率的质量检查
        if width < 800 or height < 600:
            # 低分辨率图像
            composition_issues.append("Low resolution may affect image quality")
            recommendations.append("Consider using higher resolution images")
        
        # 添加通用的构图改进建议
        recommendations.extend([
            "Apply rule of thirds for better subject placement",  # 应用三分法则
            "Check for proper exposure and contrast",  # 检查曝光和对比度
            "Ensure clear focal point in the composition"  # 确保构图有清晰的焦点
        ])
        
        return content_description, composition_issues, recommendations
    
    def get_composition_analysis(self, image: Union[Image.Image, str]) -> Dict[str, Any]:
        """获取图像构图分析的简化接口
        
        Args:
            image: PIL Image对象或图像文件路径
            
        Returns:
            Dict[str, Any]: 包含构图分析结果的字典
        """
        def _get_analysis():
            """执行构图分析并返回简化结果"""
            # 调用主要分析方法
            result = self.analyze_image(image)
            # 返回简化的结果字典
            return {
                "content_description": result.content_description,  # 内容描述
                "composition_issues": result.composition_issues,  # 构图问题
                "recommendations": result.recommendations,  # 改进建议
                "issue_count": result.issue_count,  # 问题数量
                "success": result.success  # 分析是否成功
            }

        # 直接执行分析
        try:
            return _get_analysis()
        except Exception as e:
            self.logger.error(f"Composition analysis failed: {e}")
            return {
                "content_description": "",
                "composition_issues": [],
                "recommendations": [],
                "issue_count": 0,
                "success": False
            }

    def analyze_image_properties(self, image: Union[Image.Image, str]) -> Dict[str, Any]:
        """
        Analyze basic image properties for action generation
        分析图像基本属性用于动作生成

        This method replaces functionality from IntelligentActionGenerator
        此方法替代了IntelligentActionGenerator的功能
        """
        # Load image if path is provided
        if isinstance(image, str):
            loaded_image = Image.open(image).convert("RGB")
        else:
            loaded_image = image.convert("RGB") if image.mode != "RGB" else image

        width, height = loaded_image.size
        aspect_ratio = width / height
        total_pixels = width * height

        # Determine orientation
        if aspect_ratio > 1.3:
            dominant_orientation = "landscape"
        elif aspect_ratio < 0.77:
            dominant_orientation = "portrait"
        else:
            dominant_orientation = "square"

        # Determine size category
        is_large = total_pixels > 1000000  # > 1MP
        is_small = total_pixels < 300000   # < 0.3MP

        # Analyze likely content type using basic heuristics
        likely_content = self._analyze_content_type(loaded_image, aspect_ratio)

        # Calculate basic image statistics
        import numpy as np
        img_array = np.array(loaded_image)

        # Color analysis
        avg_brightness = np.mean(img_array)
        color_variance = np.var(img_array, axis=(0, 1))

        # Determine if image is colorful or monochromatic
        is_colorful = np.std(color_variance) > 500

        analysis_result = {
            "width": width,
            "height": height,
            "aspect_ratio": aspect_ratio,
            "dominant_orientation": dominant_orientation,
            "is_large": is_large,
            "is_small": is_small,
            "likely_content": likely_content,
            "avg_brightness": float(avg_brightness),
            "is_colorful": is_colorful,
            "total_pixels": total_pixels
        }

        self.logger.info(f"📊 Image properties analysis: {likely_content} {dominant_orientation} image")
        self.logger.info(f"   Size: {width}x{height} ({total_pixels/1000000:.1f}MP)")
        self.logger.info(f"   Brightness: {avg_brightness:.1f}, Colorful: {is_colorful}")

        return analysis_result

    def _analyze_content_type(self, image: Image.Image, aspect_ratio: float) -> str:
        """
        Analyze the likely content type of the image
        分析图像的可能内容类型
        """
        # Simple heuristic-based content analysis
        # In a real implementation, this could use a trained classifier

        if aspect_ratio < 0.9:  # Portrait orientation
            return "portrait"
        elif aspect_ratio > 1.5:  # Wide landscape
            return "landscape"
        else:
            # For square-ish images, try to determine content
            # This is a simplified heuristic - in practice you'd use ML models
            width, height = image.size
            if min(width, height) < 500:
                return "general"  # Small images are often general content
            else:
                return "portrait"  # Assume larger square images might be portraits

    def generate_content_aware_recommendations(self, image: Union[Image.Image, str]) -> List[Dict[str, Any]]:
        """
        Generate content-aware editing recommendations
        生成基于内容感知的编辑建议

        This method replaces the rule-based action generation from IntelligentActionGenerator
        此方法替代了IntelligentActionGenerator中基于规则的动作生成
        """
        # Analyze image properties
        properties = self.analyze_image_properties(image)
        content_type = properties["likely_content"]
        orientation = properties["dominant_orientation"]

        recommendations = []

        # Generate recommendations based on content type
        if content_type == "portrait":
            recommendations.extend([
                {
                    "name": "crop_portrait_composition",
                    "description": "使用肖像构图规则重新裁剪，突出面部特征",
                    "expected_effect": "改善肖像构图，突出主体",
                    "category": "composition",
                    "priority": "high",
                    "reason": "Portrait images benefit from proper facial framing"
                },
                {
                    "name": "enhance_skin_tones",
                    "description": "优化肤色，使其更加自然温暖",
                    "expected_effect": "提升肖像的自然美感",
                    "category": "color_grading",
                    "priority": "medium",
                    "reason": "Skin tone enhancement improves portrait appeal"
                },
                {
                    "name": "brighten_eyes",
                    "description": "增强眼部亮度和清晰度",
                    "expected_effect": "使眼神更加有神",
                    "category": "local_adjustments",
                    "priority": "medium",
                    "reason": "Eye enhancement draws viewer attention"
                },
                {
                    "name": "blur_background_portrait",
                    "description": "模糊背景以突出人物主体",
                    "expected_effect": "创造景深效果，突出主体",
                    "category": "local_adjustments",
                    "priority": "high",
                    "reason": "Background blur creates professional portrait look"
                }
            ])

        elif content_type == "landscape":
            recommendations.extend([
                {
                    "name": "crop_landscape_composition",
                    "description": "使用风景构图规则重新裁剪，强调地平线和自然元素",
                    "expected_effect": "改善风景构图，突出自然美",
                    "category": "composition",
                    "priority": "high",
                    "reason": "Landscape images need proper horizon placement"
                },
                {
                    "name": "enhance_sky_colors",
                    "description": "增强天空色彩，使其更加戏剧化",
                    "expected_effect": "提升风景的视觉冲击力",
                    "category": "color_grading",
                    "priority": "high",
                    "reason": "Sky enhancement improves landscape drama"
                },
                {
                    "name": "enhance_natural_colors",
                    "description": "增强自然色彩，保持真实感",
                    "expected_effect": "提升色彩饱和度和生动度",
                    "category": "color_grading",
                    "priority": "medium",
                    "reason": "Natural color enhancement improves landscape appeal"
                },
                {
                    "name": "add_golden_hour_effect",
                    "description": "添加黄金时刻效果，营造温暖氛围",
                    "expected_effect": "创造温暖迷人的光线效果",
                    "category": "creative_effects",
                    "priority": "medium",
                    "reason": "Golden hour lighting enhances landscape mood"
                }
            ])

        else:  # General content
            recommendations.extend([
                {
                    "name": "improve_general_composition",
                    "description": "改善整体构图，应用构图规则",
                    "expected_effect": "提升视觉平衡和吸引力",
                    "category": "composition",
                    "priority": "medium",
                    "reason": "General composition improvement benefits all images"
                },
                {
                    "name": "enhance_colors",
                    "description": "增强色彩饱和度和对比度",
                    "expected_effect": "使图像更加生动有趣",
                    "category": "color_grading",
                    "priority": "medium",
                    "reason": "Color enhancement improves visual appeal"
                },
                {
                    "name": "sharpen_details",
                    "description": "锐化细节，提高清晰度",
                    "expected_effect": "增强图像的清晰度和细节",
                    "category": "local_adjustments",
                    "priority": "low",
                    "reason": "Detail sharpening improves image quality"
                }
            ])

        # Add brightness adjustment if needed
        if properties["avg_brightness"] < 100:  # Dark image
            recommendations.append({
                "name": "brighten_image",
                "description": "提高图像整体亮度",
                "expected_effect": "改善图像的可见性和清晰度",
                "category": "local_adjustments",
                "priority": "high",
                "reason": "Image appears too dark and needs brightening"
            })

        self.logger.info(f"💡 Generated {len(recommendations)} content-aware recommendations")
        for rec in recommendations:
            self.logger.info(f"  🎯 {rec['name']} ({rec['priority']}): {rec['description']}")

        return recommendations

    def compare_images(self, image1: Union[Image.Image, str],
                      image2: Union[Image.Image, str]) -> Dict[str, Any]:
        """比较两张图像的构图质量
        
        Args:
            image1: 第一张图像（PIL Image对象或文件路径）
            image2: 第二张图像（PIL Image对象或文件路径）
            
        Returns:
            Dict[str, Any]: 包含两张图像分析结果和比较结论的字典
        """
        def _compare():
            """执行图像比较逻辑"""
            # 分别分析两张图像
            result1 = self.analyze_image(image1)
            result2 = self.analyze_image(image2)

            # 基于问题数量进行比较（问题越少质量越好）
            issue_count1 = result1.issue_count
            issue_count2 = result2.issue_count

            # 返回详细的比较结果
            return {
                "image1_analysis": {
                    "content": result1.content_description,  # 图像1内容描述
                    "issues": result1.composition_issues,  # 图像1构图问题
                    "issue_count": issue_count1,  # 图像1问题数量
                    "success": result1.success  # 图像1分析是否成功
                },
                "image2_analysis": {
                    "content": result2.content_description,  # 图像2内容描述
                    "issues": result2.composition_issues,  # 图像2构图问题
                    "issue_count": issue_count2,  # 图像2问题数量
                    "success": result2.success  # 图像2分析是否成功
                },
                "comparison": {
                    # 根据问题数量判断哪张图像更好
                    "better_image": "image1" if issue_count1 < issue_count2 else
                                   "image2" if issue_count2 < issue_count1 else "equal",
                    "issue_difference": abs(issue_count1 - issue_count2)  # 问题数量差异
                },
                "success": result1.success and result2.success  # 整体比较是否成功
            }

        # 直接执行比较
        try:
            return _compare()
        except Exception as e:
            self.logger.error(f"Image comparison failed: {e}")
            return {
                "image1": {"success": False},
                "image2": {"success": False},
                "comparison": {"better_image": "error", "issue_difference": 0},
                "success": False
            }
