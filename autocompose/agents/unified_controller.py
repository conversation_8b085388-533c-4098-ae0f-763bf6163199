"""
Unified Controller for AutoCompose
统一的 AutoCompose 控制器

Combines the essential functionality from PlanningChainController and SimpleController
合并了 PlanningChainController 和 SimpleController 的核心功能
"""

import logging
import time
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, field
from enum import Enum
from PIL import Image

from .planning import PlanningFeedback, AdjustmentPlan, EditingInstruction, PlanningContext
from .perception import PerceptionResult
from .unified_types import ControllerDecision, OperationResult

logger = logging.getLogger(__name__)


class ControlStrategy(Enum):
    """统一的控制策略枚举"""
    CONTINUE = "continue"           # 继续执行
    RETRY = "retry"                 # 重试当前步骤
    LOCAL_ROLLBACK = "local"        # 局部回滚，重新规划当前步骤
    GLOBAL_ROLLBACK = "global"      # 全局回滚，重新规划整个计划
    ABORT = "abort"                 # 中止执行


@dataclass
class ExecutionState:
    """执行状态跟踪"""
    current_step: int = 0
    total_steps: int = 0
    retry_count: int = 0
    rollback_count: int = 0
    best_score: float = 0.0
    current_score: float = 0.0
    execution_start_time: float = field(default_factory=time.time)


class UnifiedController:
    """统一控制器 - 合并了复杂和简单控制器的核心功能"""
    
    def __init__(self, profile=None, planner=None):
        """初始化统一控制器"""
        self.profile = profile
        self.planner = planner
        
        # 配置参数 - 从 profile 中读取或使用默认值
        self.rollback_threshold = getattr(profile.profile, 'rollback_threshold', -0.1) if profile and profile.profile else -0.1
        self.max_retries = getattr(profile.profile, 'max_retries', 2) if profile and profile.profile else 2
        self.max_rollbacks = getattr(profile.profile, 'max_rollbacks', 3) if profile and profile.profile else 3
        
        # 执行状态
        self.state = ExecutionState()
        self.execution_history: List[Dict[str, Any]] = []
        
        logger.info(f"Unified controller initialized: rollback_threshold={self.rollback_threshold}, "
                   f"max_retries={self.max_retries}, max_rollbacks={self.max_rollbacks}")
    
    def start_execution(self, plan: AdjustmentPlan, perception_result: PerceptionResult) -> None:
        """开始执行计划"""
        self.state = ExecutionState(
            total_steps=len(plan.instructions),
            current_score=0.5,  # 默认初始分数
            best_score=0.5
        )
        
        logger.info(f"Started execution: {self.state.total_steps} steps")
    
    def evaluate_step_result(self, step_result: OperationResult, 
                           step_instruction: EditingInstruction) -> ControllerDecision:
        """评估步骤结果并决定下一步行动"""
        
        # 更新执行状态
        self.state.current_step += 1
        if hasattr(step_result, 'improvement'):
            self.state.current_score += step_result.improvement
            if self.state.current_score > self.state.best_score:
                self.state.best_score = self.state.current_score
        
        # 记录执行历史
        self.execution_history.append({
            'step': self.state.current_step,
            'instruction': step_instruction.instruction,
            'success': step_result.success,
            'improvement': getattr(step_result, 'improvement', 0.0),
            'timestamp': time.time()
        })
        
        # 决策逻辑
        if step_result.success and getattr(step_result, 'improvement', 0) >= 0:
            # 成功情况
            self.state.retry_count = 0  # 重置重试计数
            return ControllerDecision(
                strategy=ControlStrategy.CONTINUE.value,
                reason=f"Step {self.state.current_step} successful, improvement: {getattr(step_result, 'improvement', 0):.3f}"
            )
        
        # 失败情况 - 根据严重程度决定策略
        improvement = getattr(step_result, 'improvement', 0)
        
        if improvement < self.rollback_threshold:
            # 严重失败 - 考虑回滚
            if self.state.rollback_count < self.max_rollbacks and self.planner:
                self.state.rollback_count += 1
                strategy = ControlStrategy.LOCAL_ROLLBACK if self.state.rollback_count <= 2 else ControlStrategy.GLOBAL_ROLLBACK
                return ControllerDecision(
                    strategy=strategy.value,
                    reason=f"Severe degradation ({improvement:.3f}), attempting {strategy.value}"
                )
            else:
                return ControllerDecision(
                    strategy=ControlStrategy.ABORT.value,
                    reason="Max rollbacks reached or no planner available"
                )
        
        # 轻微失败 - 考虑重试
        if self.state.retry_count < self.max_retries:
            self.state.retry_count += 1
            return ControllerDecision(
                strategy=ControlStrategy.RETRY.value,
                reason=f"Minor failure, retry {self.state.retry_count}/{self.max_retries}"
            )
        
        # 重试次数用完 - 继续或中止
        if self.state.current_step < self.state.total_steps:
            return ControllerDecision(
                strategy=ControlStrategy.CONTINUE.value,
                reason="Max retries reached, continuing to next step"
            )
        else:
            return ControllerDecision(
                strategy=ControlStrategy.ABORT.value,
                reason="Max retries reached and no more steps"
            )
    
    def should_continue(self) -> bool:
        """判断是否应该继续执行"""
        return (self.state.current_step < self.state.total_steps and 
                self.state.rollback_count < self.max_rollbacks)
    
    def create_rollback_feedback(self, failed_instruction: EditingInstruction, 
                               step_result: OperationResult) -> PlanningFeedback:
        """创建回滚反馈信息"""
        return PlanningFeedback(
            failed_step=failed_instruction,
            failure_reason=getattr(step_result, 'error_message', 'Step execution failed'),
            score_before=self.state.current_score,
            score_after=self.state.current_score + getattr(step_result, 'improvement', 0),
            improvement=getattr(step_result, 'improvement', 0)
        )
    
    def get_execution_summary(self) -> Dict[str, Any]:
        """获取执行摘要"""
        execution_time = time.time() - self.state.execution_start_time
        
        return {
            'total_steps': self.state.total_steps,
            'completed_steps': self.state.current_step,
            'retry_count': self.state.retry_count,
            'rollback_count': self.state.rollback_count,
            'best_score': self.state.best_score,
            'current_score': self.state.current_score,
            'execution_time': execution_time,
            'success_rate': len([h for h in self.execution_history if h['success']]) / len(self.execution_history) if self.execution_history else 0,
            'average_improvement': sum(h['improvement'] for h in self.execution_history) / len(self.execution_history) if self.execution_history else 0
        }
    
    def reset(self):
        """重置控制器状态"""
        self.state = ExecutionState()
        self.execution_history.clear()
        logger.info("Controller state reset")


# 向后兼容的别名
PlanningChainController = UnifiedController


def create_controller(profile=None, planner=None, controller_type: str = "unified") -> UnifiedController:
    """控制器工厂函数"""
    if controller_type == "unified":
        return UnifiedController(profile, planner)
    else:
        # 默认返回统一控制器
        return UnifiedController(profile, planner)
