"""
Planning Agent for AutoComposeAgent

Generates adjustment plans based on perception results using LLM strategies.
"""


import json
import logging
import re
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional
from dataclasses import dataclass, field
from enum import Enum
from PIL import Image

import torch
# 导入项目内部模块
from ..core.profile import Profile  # 用户配置文件
from .unified_types import EditingInstruction, AdjustmentPlan, Priority, PerceptionResult  # 统一类型定义

# 获取当前模块的日志记录器
logger = logging.getLogger(__name__)

# ============================================================================
# 数据结构定义
# ============================================================================

class PlanningStrategy(Enum):
    """规划策略枚举 - 定义不同的规划方法"""
    CHAIN = "chain"           # 链式规划策略 - 支持重规划和步骤链接


# EditingInstruction 和 AdjustmentPlan 现在从 unified_types 导入


@dataclass
class PlanningFeedback:
    """简化的规划反馈信息 - 只保留实际使用的字段"""
    failed_step: EditingInstruction  # 失败的编辑指令
    failure_reason: str  # 失败原因描述
    score_before: float  # 执行前的评分
    score_after: float  # 执行后的评分
    improvement: float  # 改进程度（score_after - score_before）





@dataclass
class PlanningContext:
    """规划上下文信息 - 包含规划过程中需要的所有信息"""
    perception_result: PerceptionResult  # 图像感知分析结果
    image: Optional[Image.Image] = None  # 原始图像（可选）
    history: List[AdjustmentPlan] = field(default_factory=list)  # 历史规划记录
    feedback_history: List[PlanningFeedback] = field(default_factory=list)  # 反馈历史
    attempt_count: int = 0  # 当前尝试次数


# ============================================================================
# 统一 LLM 客户端
# ============================================================================

class UnifiedLLMClient:
    """统一的 LLM 客户端，消除重复代码 - 封装对不同LLM的访问"""

    def __init__(self, profile: Profile):
        """初始化LLM客户端"""
        self.profile = profile  # 保存配置文件
        self.tokenizer = None  # 分词器
        self.model = None  # 模型实例
        self.client_type = None  # 客户端类型标识
        self._initialize()  # 执行初始化
    
    def _initialize(self):
        """初始化 LLM 客户端 - 尝试加载模型"""
        try:
            self._init_local_client()  # 尝试初始化本地客户端
        except Exception as e:
            logger.error(f"LLM initialization failed: {e}")  # 记录初始化失败
            self.client_type = None  # 标记客户端不可用
    
    def _init_local_client(self):
        """初始化本地 Qwen2.5 客户端 - 加载本地模型文件"""
        try:
            # 导入transformers库
            from transformers import AutoTokenizer, AutoModelForCausalLM

            # 从 Profile 获取模型名称
            model_name = "Qwen/Qwen2.5-7B-Instruct"  # 默认模型

            # 加载分词器
            logger.info(f"Loading tokenizer for {model_name}")
            self.tokenizer = AutoTokenizer.from_pretrained(
                model_name, local_files_only=True
            )

            # 配置模型加载参数
            model_kwargs = {
                "torch_dtype": torch.bfloat16,  # 使用bfloat16精度节省内存
                "device_map": "auto",  # 自动分配设备
                "trust_remote_code": True,  # 信任远程代码
                "low_cpu_mem_usage": True  # 低CPU内存使用
            }

            # 加载模型
            logger.info(f"Loading model {model_name}")
            self.model = AutoModelForCausalLM.from_pretrained(
                model_name, **model_kwargs
            )
            
            self.client_type = "local_qwen"  # 设置客户端类型
            logger.info("Local Qwen2.5 model loaded successfully")
            
        except Exception as e:
            logger.error(f"Local model initialization failed: {e}")
            self.client_type = None  # 标记初始化失败
    
    def query(self, prompt: str) -> Optional[str]:
        """统一的查询接口 - 向LLM发送查询并获取响应"""
        if not self.client_type:  # 检查客户端是否可用
            return None

        try:
            if self.client_type == "local_qwen":
                return self._query_local_qwen(prompt)  # 查询本地模型
            else:
                logger.error("No valid LLM client available")
                return None
        except Exception as e:
            logger.error(f"LLM query failed: {e}")
            return None
    
    def _query_local_qwen(self, prompt: str) -> Optional[str]:
        """查询本地 Qwen2.5 模型 - 具体的模型查询实现"""
        try:
            # 构建对话消息
            messages = [
                {"role": "system", "content": "You are an expert image composition planning assistant."},
                {"role": "user", "content": prompt}
            ]
            
            # 应用聊天模板
            text = self.tokenizer.apply_chat_template(
                messages, tokenize=False, add_generation_prompt=True
            )
            
            # 将文本转换为模型输入
            inputs = self.tokenizer(text, return_tensors="pt").to(self.model.device)
            
            # 生成响应
            with torch.no_grad():  # 禁用梯度计算节省内存
                # 临时禁用确定性算法以避免某些操作的错误
                original_deterministic = None
                try:
                    # 检查是否启用了确定性算法
                    if hasattr(torch, 'are_deterministic_algorithms_enabled') and torch.are_deterministic_algorithms_enabled():
                        original_deterministic = True
                        torch.use_deterministic_algorithms(False)
                except:
                    pass

                try:
                    # 执行模型生成
                    outputs = self.model.generate(
                        **inputs,
                        max_new_tokens=2000,  # 最大新token数
                        temperature=0.3,  # 随机性控制
                        top_p=0.8,  # 核采样参数
                        do_sample=True,  # 启用采样
                        pad_token_id=self.tokenizer.eos_token_id  # 填充token ID
                    )
                finally:
                    # 恢复原始的确定性算法设置
                    if original_deterministic is not None:
                        try:
                            torch.use_deterministic_algorithms(True, warn_only=True)
                        except:
                            try:
                                torch.use_deterministic_algorithms(True)
                            except:
                                pass
            
            # 解码生成的响应
            response = self.tokenizer.decode(
                outputs[0][inputs['input_ids'].shape[1]:],  # 只解码新生成的部分
                skip_special_tokens=True  # 跳过特殊token
            )
            return response.strip()  # 返回去除首尾空格的响应
        except Exception as e:
            logger.error(f"Local model query failed: {e}")
            return None
    
    def is_available(self) -> bool:
        """检查客户端是否可用"""
        return self.client_type is not None


# ============================================================================
# 规划策略抽象基类和实现
# ============================================================================

class PlanningStrategyBase(ABC):
    """规划策略抽象基类 - 定义所有规划策略的通用接口"""

    def __init__(self, llm_client: UnifiedLLMClient, profile: Profile):
        """初始化策略基类"""
        self.llm_client = llm_client  # LLM客户端实例
        self.profile = profile  # 配置信息

    @abstractmethod
    def generate_plan(self, context: PlanningContext) -> Optional[AdjustmentPlan]:
        """生成规划 - 抽象方法，子类必须实现"""
        pass

    @abstractmethod
    def replan(self, feedback: PlanningFeedback, context: PlanningContext) -> Optional[AdjustmentPlan]:
        """重新规划 - 抽象方法，子类必须实现"""
        pass

    def extract_instructions_from_response(self, response: str) -> List[str]:
        """统一的指令提取方法 - 从LLM响应中提取编辑指令"""
        try:
            # 首先尝试提取 <instructions> 部分
            instructions_match = re.search(r'<instructions>(.*?)</instructions>', response, re.DOTALL)

            if instructions_match:
                instructions_text = instructions_match.group(1).strip()
            else:
                # 如果没有找到 <instructions> 标签，尝试其他格式
                logger.info("No <instructions> section found, trying alternative extraction")

                # 尝试查找编号列表
                numbered_pattern = r'(?:^|\n)\s*(\d+)[\.\)\-:]\s*(.*?)(?=(?:\n\s*\d+[\.\)\-:])|$)'
                numbered_matches = re.findall(numbered_pattern, response, re.DOTALL | re.MULTILINE)

                if numbered_matches:
                    instructions = []
                    for match in numbered_matches:
                        instruction_text = match[1].strip()
                        if instruction_text and len(instruction_text) > 10:  # 过滤太短的文本
                            instructions.append(instruction_text)
                    if instructions:
                        logger.info(f"Extracted {len(instructions)} instructions using numbered pattern")
                        return instructions

                # 最后尝试：按行分割并过滤
                lines = [line.strip() for line in response.split('\n') if line.strip()]
                potential_instructions = []
                for line in lines:
                    # 跳过明显不是指令的行
                    if (len(line) > 20 and
                        not line.startswith(('Input', 'Output', 'Requirements', '---', 'Available')) and
                        not line.endswith(':') and
                        ('crop' in line.lower() or 'adjust' in line.lower() or 'improve' in line.lower() or
                         'enhance' in line.lower() or 'remove' in line.lower() or 'rotate' in line.lower())):
                        potential_instructions.append(line)

                if potential_instructions:
                    logger.info(f"Extracted {len(potential_instructions)} instructions using fallback method")
                    return potential_instructions[:4]  # 限制最多4个指令

                logger.warning("No valid instructions found in response")
                return []

            # 解析编号指令
            # instruction_matches = re.findall(r'(?:^|\n)\s*(\d+)[\.\)\-:]\s*(.*?)(?=(?:\n\s*\d+[\.\)\-:])|$)',
            #                                instructions_text, re.DOTALL | re.MULTILINE)
            instruction_matches = re.findall(
                r'(?:^|\n)\s*(\d+)[\.\)\-:]\s*([\s\S]*?)(?=(?:\n\s*\d+[\.\)\-:])|$)',
                instructions_text,
                re.DOTALL | re.MULTILINE
            )
            if not instruction_matches:
                # 后备方案：按行分割
                lines = [line.strip() for line in instructions_text.split('\n') if line.strip()]
                return [line for line in lines if line and len(line) > 10]

            # 提取指令文本
            instructions = []
            for _, instruction in instruction_matches:
                instruction_clean = instruction.strip()
                if instruction_clean:
                    instructions.append(instruction_clean)

            return instructions

        except Exception as e:
            logger.error(f"Instruction extraction failed: {e}")
            return []

    def create_adjustment_plan(self, instructions: List[str], context: PlanningContext,
                             strategy: PlanningStrategy) -> AdjustmentPlan:
        """统一的计划创建方法 - 将指令列表转换为调整计划"""
        editing_instructions = []
        for i, instruction_text in enumerate(instructions):
            # 使用 Priority 枚举而不是整数，第一个指令设为高优先级
            priority = Priority.HIGH if i == 0 else Priority.NORMAL
            editing_instruction = EditingInstruction(
                instruction=instruction_text,
                priority=priority,
                step_id=f"step_{i+1}"  # 为每个步骤分配唯一的 ID
            )
            editing_instructions.append(editing_instruction)

        # 创建并返回调整计划
        return AdjustmentPlan(
            instructions=editing_instructions
        )

class ChainPlanningStrategy(PlanningStrategyBase):
    """链式规划策略 - 支持重规划的高级策略，具有历史记录和智能重规划功能"""

    def __init__(self, llm_client: UnifiedLLMClient, profile: Profile):
        """初始化链式规划策略"""
        super().__init__(llm_client, profile)
        self.planning_history = []  # 规划历史记录
        self.feedback_history = []  # 反馈历史记录

    def _create_comprehensive_planning_prompt(self, perception_result: PerceptionResult) -> str:
        """创建综合规划提示词"""
        prompt = f"""
            You are a professional image composition planning assistant. Your task is to create a comprehensive multi-step plan for improving image composition based on the analysis provided.
            ---
            Input Analysis:
            - Image Content: {getattr(perception_result, 'content_description', 'Unknown content')}
            - Composition Issues: {getattr(perception_result, 'composition_issues', ['No issues identified'])}
            - Recommendations: {getattr(perception_result, 'recommendations', ['No recommendations available'])}
            ---
            Your task is to create a comprehensive multi-step editing plan. Consider the following:
            1. **Plan Structure**: Create 3-5 editing steps that build upon each other
            2. **Step Ordering**: Arrange steps from most impactful to fine-tuning
            3. **Coherence**: Ensure each step complements the others
            4. **Feasibility**: Each step should be technically achievable】


            Available Operations:
            - Cropping and framing adjustments
            - Rotation and straightening
            - Brightness, contrast, and exposure adjustments
            - Color correction and white balance
            - Background blur and depth effects
            - Element removal (but not addition)

            The answer should be strictly follows the output Format:
            <instructions>
            1. [First editing step - most impactful change]
            2. [Second editing step - builds on the first]
            3. [Third editing step - further refinement]
            4. [Fourth editing step - fine-tuning if needed]
            5. [Fifth editing step - final polish if needed]
            </instructions>

            Requirements:
            - Each instruction must be clear and actionable
            - Focus on composition and aesthetic improvements
            - Consider the relationship between steps
            - Prioritize changes that will have the most positive impact
            """
        return prompt

    def generate_plan(self, context: PlanningContext) -> Optional[AdjustmentPlan]:
        """生成链式规划 - 使用更复杂的提示词和上下文信息"""
        if not self.llm_client.is_available():
            return None

        try:
            # 创建综合规划提示词
            prompt = self._create_comprehensive_planning_prompt(context.perception_result)
            # 查询LLM获取响应
            response = self.llm_client.query(prompt)
            if not response:
                return None


            # 提取编辑指令
            instructions = self.extract_instructions_from_response(response)
            
            if not instructions:
                return None

            # 创建调整计划
            plan = self.create_adjustment_plan(
                instructions, context, PlanningStrategy.CHAIN
            )
            # 记录到历史中
            self.planning_history.append(plan)
            logger.info(f"Generated chain plan with {len(plan.instructions)} steps")
            return plan

        except Exception as e:
            logger.error(f"Chain plan generation failed: {e}")
            return None

    def replan(self, feedback: PlanningFeedback, context: PlanningContext) -> Optional[AdjustmentPlan]:
        """智能重规划方法 - 根据失败严重程度选择合适的重规划策略"""
        try:
            # 判断失败严重程度
            failure_severity = self._assess_failure_severity(feedback, context)

            # 根据严重程度选择重规划策略
            if failure_severity == "minor":
                logger.info("Using local replanning for minor failure")
                return self.replan_local(feedback, context)
            
            if failure_severity == "moderate":
                logger.info("Trying local replanning first for moderate failure")
                local_result = self.replan_local(feedback, context)
                if local_result:
                    return local_result
                logger.info("Local replanning failed, falling back to global replanning")
            
            # severe 或 moderate 的全局重规划
            logger.info("Using global replanning for severe or fallback from moderate failure")
            return self.replan_global(feedback, context)

        except Exception as e:
            logger.error(f"Intelligent replanning failed: {e}, falling back to global replanning")
            return self.replan_global(feedback, context)

    def _assess_failure_severity(self, feedback: PlanningFeedback, context: PlanningContext) -> str:
        """评估失败严重程度"""
        try:
            # 直接使用统一的 PlanningFeedback
            improvement = feedback.improvement
            if improvement >= -0.05:  # 轻微负面影响
                return "minor"
            elif improvement >= -0.15:  # 中等负面影响
                return "moderate"
            else:  # 严重负面影响
                return "severe"

        except Exception as e:
            logger.error(f"Failure severity assessment failed: {e}")
            return "moderate"  # 默认中等严重程度

    def _create_alternative_instruction(self, failed_step: EditingInstruction,
                                      feedback: PlanningFeedback) -> EditingInstruction:
        """使用 Agent-R 反思机制创建智能的替换指令"""
        try:
            # 导入反思引擎
            from .reflection import ReflectionEngine
            from .trajectory import TrajectoryStep, EditingTrajectory
            from .unified_types import OperationResult

            # 创建反思引擎
            reflection_engine = ReflectionEngine(self.llm_client)

            # 构建轨迹步骤用于反思分析
            trajectory_step = TrajectoryStep(
                step_id=failed_step.step_id or "unknown",
                instruction=failed_step,
                result=OperationResult(
                    success=False,
                    instruction=failed_step.instruction,
                    improvement=feedback.improvement
                ),
                timestamp=time.time(),
                score_before=feedback.score_before,
                score_after=feedback.score_after,
                improvement=feedback.improvement
            )

            # 创建简化的轨迹上下文
            trajectory_context = EditingTrajectory(
                trajectory_id="temp_reflection",
                image_path="unknown",
                steps=[trajectory_step]
            )

            # 使用反思引擎分析失败
            reflection_analysis = reflection_engine.analyze_failure(trajectory_step, trajectory_context)

            # 如果反思分析生成了替代指令，使用它
            if reflection_analysis.alternative_instruction:
                logger.info(f"Agent-R reflection generated alternative: {reflection_analysis.alternative_instruction.instruction}")
                return reflection_analysis.alternative_instruction

            # 否则使用原有的 Qwen2.5 方法作为后备
            prompt = self._create_local_replan_prompt(failed_step, feedback)
            response = self.llm_client.query(prompt)

            if response:
                new_instruction_text = self._extract_single_instruction(response)
                if new_instruction_text:
                    return EditingInstruction(
                        instruction=new_instruction_text,
                        priority=failed_step.priority,
                        step_id=f"replan_{failed_step.step_id}"
                    )

            raise ValueError("Failed to generate alternative instruction")

        except Exception as e:
            logger.error(f"Error creating alternative instruction with Agent-R: {e}")
            raise

    def _create_local_replan_prompt(self, failed_step: EditingInstruction,
                                  feedback: PlanningFeedback) -> str:
        """创建局部重规划的提示词"""
        prompt = f"""
        You are an expert image editing assistant. A specific editing step has failed and needs to be replanned.

        FAILED STEP INFORMATION:
        - Original instruction: "{failed_step.instruction}"
        - Failure reason: "{feedback.failure_reason}"
        - Score before: {feedback.score_before:.3f}
        - Score after: {feedback.score_after:.3f}
        - Improvement: {feedback.improvement:.3f} (negative means degradation)

        TASK:
        Generate ONE alternative editing instruction that addresses the failure while achieving the same goal.
        The alternative should be:
        1. More conservative/gentle than the original
        2. Address the specific failure reason
        3. Use different techniques or parameters
        4. Be specific and actionable

        EXAMPLES:
        - If "Crop image to center subject" failed due to composition issues → "Apply rule of thirds cropping with subject in intersection points"
        - If "Increase brightness by 20%" failed due to overexposure → "Apply subtle exposure adjustment with highlight protection"
        - If "Enhance contrast globally" failed → "Apply adaptive local contrast enhancement"

        OUTPUT FORMAT:
        Provide only the alternative instruction text, no explanations or additional text.

        Alternative instruction:"""

        return prompt

    def _extract_single_instruction(self, response: str) -> Optional[str]:
        """从 LLM 响应中提取单个指令"""
        try:
            # 清理响应文本
            response = response.strip()

            # 查找 "Alternative instruction:" 后的内容
            if "Alternative instruction:" in response:
                instruction = response.split("Alternative instruction:")[-1].strip()
            else:
                # 如果没有找到标记，使用整个响应
                instruction = response

            # 清理指令文本
            instruction = instruction.strip()

            # 移除可能的引号
            if instruction.startswith('"') and instruction.endswith('"'):
                instruction = instruction[1:-1]
            elif instruction.startswith("'") and instruction.endswith("'"):
                instruction = instruction[1:-1]

            return instruction

        except Exception as e:
            logger.error(f"Error extracting instruction from response: {e}")
            return None



    def _replace_failed_step_in_plan(self, current_plan: AdjustmentPlan,
                                   failed_step: EditingInstruction,
                                   new_instruction: EditingInstruction) -> AdjustmentPlan:
        """在计划中替换失败的步骤"""
        new_instructions = []
        for inst in current_plan.instructions:
            if inst.step_id == failed_step.step_id:
                new_instructions.append(new_instruction)
            else:
                new_instructions.append(inst)
        print('new_instructions', new_instructions)
        return AdjustmentPlan(instructions=new_instructions)

    def replan_local(self, feedback: PlanningFeedback, context: PlanningContext) -> Optional[AdjustmentPlan]:
        """局部重规划 - 使用 Qwen2.5 重新生成失败的单个步骤"""
        try:
            failed_step = feedback.failed_step
            logger.info(f"Starting local replan for failed step: {failed_step.instruction}")
            logger.info(f"Failure reason: {feedback.failure_reason}")
            logger.info(f"Performance impact: {feedback.improvement:.3f}")

            # 使用 Qwen2.5 创建智能的替换指令
            new_instruction = self._create_alternative_instruction(failed_step, feedback)
            logger.info(f"Generated alternative instruction: {new_instruction.instruction}")
            print('context_history', context.history)
            # 更新计划
            if context.history:
                current_plan = context.history[-1]
                new_plan = self._replace_failed_step_in_plan(current_plan, failed_step, new_instruction)

                self.planning_history.append(new_plan)
                self.feedback_history.append(feedback)

                logger.info(f"Local replan completed successfully")
                print('new_plan', new_plan)
                print('self.planning_history', self.planning_history)
                print('self.feedback_history', self.feedback_history)
                return new_plan
      
            logger.warning("No history available for local replanning")
            return None

        except Exception as e:
            logger.error(f"Local replanning failed: {e}")
            return None

    def replan_global(self, feedback: PlanningFeedback, context: PlanningContext) -> Optional[AdjustmentPlan]:
        """全局重规划 - 基于反馈重新生成完整计划"""
        if not self.llm_client.is_available():
            return None

        try:
            # 简化的全局重规划：重新生成计划
            prompt = f"""
                Create a new image editing plan. Previous attempt failed: {feedback.failure_reason}

                <instructions>
                1. Crop image to improve composition
                2. Adjust brightness and contrast
                3. Apply color correction
                </instructions>
                """

            response = self.llm_client.query(prompt)
            if not response:
                # 使用后备计划
                instructions = ["Crop image to center subject", "Adjust exposure", "Enhance colors"]
            else:
                instructions = self.extract_instructions_from_response(response)
                if not instructions:
                    instructions = ["Crop image to center subject", "Adjust exposure", "Enhance colors"]

            # 创建新计划
            new_plan = self.create_adjustment_plan(
                instructions, context, PlanningStrategy.CHAIN
            )

            self.planning_history.append(new_plan)
            self.feedback_history.append(feedback)
            return new_plan

        except Exception as e:
            logger.error(f"Global replanning failed: {e}")
            return None










# ============================================================================
# 门面类 - 保持向后兼容性
# ============================================================================

class PlanningAgent:
    """规划代理 - 简化的核心规划实现"""

    def __init__(self, profile: Profile):
        """初始化规划代理"""
        self.profile = profile
        self.llm_client = UnifiedLLMClient(self.profile)
        self.strategy = ChainPlanningStrategy(self.llm_client, self.profile)

        # 历史记录
        self.planning_history = []
        self.feedback_history = []

        logger.info("PlanningAgent initialized with simplified architecture")

    def generate_plan(self, perception_result: PerceptionResult,
                     image: Optional[Image.Image] = None) -> AdjustmentPlan:
        """生成调整计划"""
        try:
            # 创建规划上下文
            context = PlanningContext(
                perception_result=perception_result,
                image=image,
                history=self.planning_history,
                feedback_history=self.feedback_history
            )
            plan = self.strategy.generate_plan(context)
            if plan:
                self.planning_history.append(plan)
                return plan
            else:
                # 如果规划器失败，返回最小后备计划
                return self._generate_minimal_fallback_plan(perception_result)
        except Exception as e:
            logger.error(f"Plan generation failed: {e}")
            return self._generate_minimal_fallback_plan(perception_result)

    def replan_local(self, failed_feedback: PlanningFeedback,
                    current_plan: AdjustmentPlan) -> Optional[AdjustmentPlan]:
        """局部重规划 - 重新生成失败的单个步骤，返回完整的新计划"""
        try:
            # 直接使用统一的 PlanningFeedback
            self.feedback_history.append(failed_feedback)

            # 创建局部重规划上下文
            context = PlanningContext(
                perception_result=PerceptionResult(
                    success=True,
                    content_description="Local replanning context",
                    composition_issues=["Step failed"],
                    recommendations=["Try alternative approach"]
                ),
                history=[current_plan],
                feedback_history=[failed_feedback]
            )

            # 使用策略进行局部重规划，直接返回完整计划
            new_plan = self.strategy.replan_local(failed_feedback, context)
            if new_plan:
                self.planning_history.append(new_plan)
            return new_plan

        except Exception as e:
            logger.error(f"Local replanning failed: {e}")
            return None

    def replan_global(self, failed_feedback: PlanningFeedback,
                     perception_result: PerceptionResult) -> Optional[AdjustmentPlan]:
        """全局重规划 - 基于反馈重新生成完整计划"""
        try:
            # 直接使用统一的 PlanningFeedback
            self.feedback_history.append(failed_feedback)

            # 创建全局重规划上下文
            context = PlanningContext(
                perception_result=perception_result,
                history=self.planning_history,
                feedback_history=self.feedback_history
            )

            # 使用策略进行全局重规划
            new_plan = self.strategy.replan_global(failed_feedback, context)
            if new_plan:
                self.planning_history.append(new_plan)
            return new_plan

        except Exception as e:
            logger.error(f"Global replanning failed: {e}")
            return None



    def _generate_minimal_fallback_plan(self, perception_result: PerceptionResult) -> AdjustmentPlan:
        """生成最小后备计划 - 最后的保障方案"""
        from .unified_types import Priority

        # 创建基本指令
        instruction = EditingInstruction(
            instruction="Crop the image to improve composition using center framing",
            priority=Priority.NORMAL,
            step_id="fallback_step_1"  # 为后备指令分配 ID
        )

        # 返回最简计划
        return AdjustmentPlan(
            instructions=[instruction]
        )
