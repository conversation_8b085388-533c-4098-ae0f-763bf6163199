"""
Vision-Guided Action Planner

Integrates LLaVA-Vision perception with LLM planning for intelligent action generation.
集成 LLaVA-Vision 感知与 LLM 规划，实现智能动作生成。
"""

import logging
from typing import List, Dict, Any, Optional
from PIL import Image
import json

from .perception import PerceptionAgent
from .planning import UnifiedLLMClient

logger = logging.getLogger(__name__)


class VisionGuidedPlanner:
    """基于视觉感知的智能规划器"""
    
    def __init__(self, profile, llm_client: Optional[UnifiedLLMClient] = None):
        self.profile = profile
        
        # 初始化感知代理（使用 LLaVA-Vision）
        self.perception_agent = PerceptionAgent(profile)
        
        # 初始化 LLM 客户端
        self.llm_client = llm_client or UnifiedLLMClient(profile)
        
        logger.info("Vision-guided planner initialized with LLaVA-Vision perception")
    
    def generate_vision_guided_actions(self, image: Image.Image, max_actions: int = 8) -> List[Dict[str, Any]]:
        """
        Generate vision-guided actions using the new architecture
        使用新架构生成基于视觉感知的动作
        """
        logger.info("🔍 Starting vision-guided action generation with new architecture...")

        # Step 1: 使用 Perception Agent 进行图像属性分析
        logger.info("👁️  Performing image properties analysis...")
        try:
            # Get basic image properties
            image_properties = self.perception_agent.analyze_image_properties(image)

            # Get detailed perception analysis
            perception_result = self.perception_agent.analyze_image(image)

            # Log perception results
            logger.info(f"📝 Content type: {image_properties['likely_content']}")
            logger.info(f"📐 Orientation: {image_properties['dominant_orientation']}")
            logger.info(f"🚨 Issues found: {len(perception_result.composition_issues)}")

        except Exception as e:
            logger.error(f"❌ Perception analysis failed: {e}")
            # Create fallback properties
            image_properties = {
                "likely_content": "general",
                "dominant_orientation": "landscape",
                "is_large": True,
                "avg_brightness": 128,
                "is_colorful": True
            }
            from .unified_types import PerceptionResult
            perception_result = PerceptionResult(
                content_description="Image analysis unavailable",
                composition_issues=[],
                recommendations=[],
                overall_score=0.5
            )

        # Step 2: 使用 Planning Agent 生成智能动作
        logger.info("🧠 Generating intelligent actions using Planning Agent...")
        try:
            from .planning import PlanningAgent
            planning_agent = PlanningAgent(self.profile)

            actions = planning_agent.generate_intelligent_actions(
                image_properties=image_properties,
                perception_result=perception_result,
                max_actions=max_actions
            )

            if not actions:
                logger.warning("⚠️  Planning agent returned no actions, using fallback")
                return self._generate_fallback_actions(perception_result)

            logger.info(f"✅ Successfully generated {len(actions)} vision-guided actions")
            return actions

        except Exception as e:
            logger.error(f"❌ Planning agent failed: {e}")
            logger.info("🔄 Falling back to perception-based recommendations")

            # Fallback to perception-based recommendations
            try:
                recommendations = self.perception_agent.generate_content_aware_recommendations(image)
                if recommendations:
                    return recommendations[:max_actions]
            except Exception as e2:
                logger.error(f"❌ Perception recommendations also failed: {e2}")

            # Final fallback
            return self._generate_fallback_actions(perception_result)
    
    def _create_vision_guided_prompt(self, perception_result, image: Image.Image) -> str:
        """基于视觉感知结果创建 LLM 规划提示"""
        
        # 提取感知结果的关键信息
        content_desc = perception_result.content_description
        issues = perception_result.composition_issues
        recommendations = perception_result.recommendations
        
        # 获取图像基本信息
        width, height = image.size
        aspect_ratio = width / height
        
        prompt = f"""
作为专业的图像编辑专家，请基于以下详细的视觉分析结果，为图像生成具体的编辑动作计划。

## 图像基本信息
- 尺寸: {width} x {height}
- 宽高比: {aspect_ratio:.2f}

## LLaVA-Vision 感知分析结果

### 内容描述
{content_desc}

### 识别的构图问题
{chr(10).join([f"- {issue}" for issue in issues]) if issues else "- 未发现明显构图问题"}

### 改进建议
{chr(10).join([f"- {rec}" for rec in recommendations]) if recommendations else "- 图像质量良好"}

## 任务要求

请基于以上视觉分析，生成6-8个具体的图像编辑动作。每个动作应该：

1. **针对性强**: 直接解决识别出的具体问题
2. **可执行**: 提供明确的操作指导
3. **有层次**: 从构图、色彩、局部调整到创意效果

### 动作类别参考
- **构图优化**: 裁剪、重新构图、调整视角、平衡元素
- **色彩调整**: 色调、饱和度、对比度、色彩平衡
- **局部增强**: 主体突出、背景处理、细节锐化
- **语义编辑**: 移除干扰物、增强特定元素
- **创意效果**: 景深、氛围、风格化处理

### 输出格式
请按以下格式输出每个动作：

```
action_name: 动作的简洁英文标识符
description: 详细描述要执行的具体操作
reason: 基于视觉分析的执行理由
category: 动作类别 (composition/color_grading/local_adjustments/semantic_edits/creative_effects)
priority: 优先级 (high/medium/low)
```

### 示例
```
crop_rule_of_thirds: 使用三分法则重新裁剪图像，将主要元素放置在交叉点
description: 根据当前构图分析，将主体重新定位到三分线交叉点以改善视觉平衡
reason: 感知分析显示当前主体位置偏离最佳构图点
category: composition
priority: high
```

请开始生成动作计划：
"""
        
        return prompt
    
    def _parse_action_plan(self, llm_response: str) -> List[Dict[str, Any]]:
        """解析 LLM 生成的动作计划"""
        actions = []
        
        # 按行分割响应
        lines = llm_response.strip().split('\n')
        current_action = {}
        
        for line in lines:
            line = line.strip()
            if not line or line.startswith('#') or line.startswith('```'):
                continue
            
            # 解析动作字段
            if ':' in line:
                key, value = line.split(':', 1)
                key = key.strip().lower()
                value = value.strip()
                
                if key in ['action_name', 'name']:
                    # 开始新动作
                    if current_action:
                        actions.append(current_action)
                    current_action = {'name': value}
                elif key == 'description':
                    current_action['description'] = value
                elif key == 'reason':
                    current_action['reason'] = value
                elif key == 'category':
                    current_action['category'] = value
                elif key == 'priority':
                    current_action['priority'] = value
        
        # 添加最后一个动作
        if current_action:
            actions.append(current_action)
        
        # 验证和清理动作
        validated_actions = []
        for action in actions:
            if 'name' in action and 'description' in action:
                # 设置默认值
                action.setdefault('category', 'general')
                action.setdefault('priority', 'medium')
                action.setdefault('reason', 'Based on visual analysis')
                validated_actions.append(action)
        
        logger.info(f"📝 Parsed {len(validated_actions)} valid actions from LLM response")
        return validated_actions
    
    def _generate_fallback_actions(self, perception_result) -> List[Dict[str, Any]]:
        """基于感知结果生成回退动作"""
        logger.info("🔄 Generating fallback actions based on perception results...")

        actions = []
        issues = perception_result.composition_issues if perception_result else []
        recommendations = perception_result.recommendations if perception_result else []

        logger.info(f"📊 Fallback input: {len(issues)} issues, {len(recommendations)} recommendations")
        
        # 基于识别的问题生成针对性动作
        if any("crop" in issue.lower() or "composition" in issue.lower() for issue in issues):
            actions.append({
                "name": "improve_composition_crop",
                "description": "根据构图分析重新裁剪图像以改善视觉平衡",
                "category": "composition",
                "priority": "high",
                "reason": "感知分析识别出构图问题"
            })
        
        if any("color" in issue.lower() or "saturation" in issue.lower() for issue in issues):
            actions.append({
                "name": "enhance_color_balance",
                "description": "调整色彩平衡和饱和度以改善视觉效果",
                "category": "color_grading", 
                "priority": "high",
                "reason": "感知分析识别出色彩问题"
            })
        
        if any("contrast" in issue.lower() or "brightness" in issue.lower() for issue in issues):
            actions.append({
                "name": "adjust_exposure_contrast",
                "description": "优化曝光和对比度以提升图像质量",
                "category": "local_adjustments",
                "priority": "medium",
                "reason": "感知分析识别出曝光或对比度问题"
            })
        
        # 基于建议生成动作
        for rec in recommendations:
            if "sharpen" in rec.lower():
                actions.append({
                    "name": "enhance_sharpness",
                    "description": "增强图像锐度以改善细节表现",
                    "category": "local_adjustments",
                    "priority": "medium",
                    "reason": f"基于建议: {rec}"
                })
            elif "blur" in rec.lower() and "background" in rec.lower():
                actions.append({
                    "name": "blur_background_focus",
                    "description": "模糊背景以突出主体",
                    "category": "local_adjustments",
                    "priority": "medium",
                    "reason": f"基于建议: {rec}"
                })
        
        # 如果没有特定问题，添加多样化的通用改进动作
        if not actions:
            actions.extend([
                {
                    "name": "crop_rule_of_thirds",
                    "description": "使用三分法则重新裁剪图像",
                    "category": "composition",
                    "priority": "high",
                    "reason": "改善构图平衡"
                },
                {
                    "name": "enhance_color_saturation",
                    "description": "适度增强色彩饱和度",
                    "category": "color_grading",
                    "priority": "medium",
                    "reason": "提升色彩鲜活度"
                },
                {
                    "name": "adjust_brightness_contrast",
                    "description": "优化亮度和对比度",
                    "category": "local_adjustments",
                    "priority": "medium",
                    "reason": "改善图像质量"
                },
                {
                    "name": "sharpen_image_details",
                    "description": "锐化图像细节",
                    "category": "local_adjustments",
                    "priority": "low",
                    "reason": "增强清晰度"
                },
                {
                    "name": "add_subtle_vignette",
                    "description": "添加轻微的暗角效果",
                    "category": "creative_effects",
                    "priority": "low",
                    "reason": "增强视觉焦点"
                },
                {
                    "name": "warm_color_tone",
                    "description": "添加温暖的色调",
                    "category": "color_grading",
                    "priority": "low",
                    "reason": "营造温馨氛围"
                }
            ])
        
        # 确保至少有基本的多样化动作集
        if len(actions) < 4:
            logger.info("⚠️  Too few actions generated, adding essential actions")
            essential_actions = self._get_essential_actions()

            # 添加不重复的基本动作
            for essential in essential_actions:
                if not any(action['name'] == essential['name'] for action in actions):
                    actions.append(essential)
                    if len(actions) >= 6:  # 限制总数
                        break

        logger.info(f"🔄 Generated {len(actions)} fallback actions")
        for action in actions:
            logger.info(f"  📝 {action['name']}: {action['description']}")

        return actions

    def _get_essential_actions(self) -> List[Dict[str, Any]]:
        """获取基本的必需动作集合"""
        return [
            {
                "name": "crop_rule_of_thirds",
                "description": "使用三分法则重新裁剪图像",
                "category": "composition",
                "priority": "high",
                "reason": "基础构图优化"
            },
            {
                "name": "enhance_color_saturation",
                "description": "适度增强色彩饱和度",
                "category": "color_grading",
                "priority": "medium",
                "reason": "色彩增强"
            },
            {
                "name": "adjust_brightness_contrast",
                "description": "优化亮度和对比度",
                "category": "local_adjustments",
                "priority": "medium",
                "reason": "曝光优化"
            },
            {
                "name": "sharpen_image_details",
                "description": "锐化图像细节",
                "category": "local_adjustments",
                "priority": "low",
                "reason": "细节增强"
            },
            {
                "name": "add_subtle_vignette",
                "description": "添加轻微的暗角效果",
                "category": "creative_effects",
                "priority": "low",
                "reason": "视觉焦点"
            },
            {
                "name": "warm_color_tone",
                "description": "添加温暖的色调",
                "category": "color_grading",
                "priority": "low",
                "reason": "氛围营造"
            }
        ]
    
    def get_perception_summary(self, image: Image.Image) -> Dict[str, Any]:
        """获取感知分析摘要"""
        perception_result = self.perception_agent.analyze_image(image)
        
        return {
            "content_description": perception_result.content_description,
            "composition_issues": perception_result.composition_issues,
            "recommendations": perception_result.recommendations,
            "issue_count": len(perception_result.composition_issues),
            "recommendation_count": len(perception_result.recommendations)
        }
