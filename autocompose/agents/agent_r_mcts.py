"""
Agent-R MCTS Implementation
基于 Agent-R 的蒙特卡洛树搜索实现

Pure Agent-R approach with MCTS trajectory exploration and rollback
纯 Agent-R 方法，包含 MCTS 轨迹探索和回滚机制
"""

import math
import random
import logging
import time
from typing import Dict, List, Optional, Tuple, Any, Callable
from dataclasses import dataclass, field
from PIL import Image, ImageEnhance

logger = logging.getLogger(__name__)


@dataclass
class MCTSNode:
    """MCTS 节点 - Agent-R 风格"""
    node_id: str
    action: Optional[str] = None
    parent: Optional['MCTSNode'] = None
    children: List['MCTSNode'] = field(default_factory=list)
    visits: int = 0
    total_reward: float = 0.0
    image_state: Optional[Image.Image] = None
    depth: int = 0
    
    @property
    def average_reward(self) -> float:
        return self.total_reward / self.visits if self.visits > 0 else 0.0
    
    @property
    def is_leaf(self) -> bool:
        return len(self.children) == 0
    
    def ucb1_score(self, c: float = 1.414) -> float:
        """UCB1 选择策略"""
        if self.visits == 0:
            return float('inf')
        if self.parent is None or self.parent.visits == 0:
            return self.average_reward
        
        exploitation = self.average_reward
        exploration = c * math.sqrt(math.log(self.parent.visits) / self.visits)
        return exploitation + exploration
    
    def rollback_to_parent(self) -> Optional['MCTSNode']:
        """回滚到父节点 - Agent-R 核心机制"""
        if self.parent is None:
            return None
        
        # 从父节点的子节点中移除当前节点
        if self in self.parent.children:
            self.parent.children.remove(self)
        
        logger.debug(f"Rolled back from node {self.node_id} to parent {self.parent.node_id}")
        return self.parent
    
    def get_path_to_root(self) -> List['MCTSNode']:
        """获取到根节点的路径"""
        path = []
        current = self
        while current is not None:
            path.append(current)
            current = current.parent
        return list(reversed(path))


@dataclass
class Trajectory:
    """简化的轨迹表示"""
    trajectory_id: str
    nodes: List[MCTSNode] = field(default_factory=list)
    total_reward: float = 0.0
    success: bool = False
    
    def add_node(self, node: MCTSNode):
        self.nodes.append(node)
        self.total_reward += node.average_reward
    
    @property
    def length(self) -> int:
        return len(self.nodes)


class AgentRMCTS:
    """Agent-R MCTS 搜索器"""

    def __init__(self, max_iterations: int = 100, max_depth: int = 6,
                 rollback_threshold: float = -0.2, llm_client=None, profile=None):
        self.max_iterations = max_iterations
        self.max_depth = max_depth
        self.rollback_threshold = rollback_threshold

        # 基于视觉感知的智能规划器和图像处理器
        from .vision_guided_planner import VisionGuidedPlanner
        from .intelligent_image_processor import IntelligentImageProcessor

        self.vision_planner = VisionGuidedPlanner(profile=profile, llm_client=llm_client)
        self.image_processor = IntelligentImageProcessor()

        # 动态生成的动作空间（将在搜索开始时生成）
        self.actions = []
        self.action_details = {}  # 存储动作的详细信息

        # 统计信息
        self.stats = {
            'total_rollbacks': 0,
            'successful_trajectories': 0,
            'failed_trajectories': 0
        }
    
    def search(self, initial_image: Image.Image, 
               evaluator: Callable[[Image.Image, str], float]) -> List[Trajectory]:
        """执行 Agent-R MCTS 搜索"""
        
        # 创建根节点
        root = MCTSNode(
            node_id="root_0",
            image_state=initial_image.copy(),
            depth=0
        )
        
        trajectories = []

        # 使用 LLaVA-Vision 感知 + LLM 规划生成智能动作空间
        logger.info(f"👁️  Generating vision-guided actions using LLaVA-Vision + LLM...")
        vision_guided_actions = self.vision_planner.generate_vision_guided_actions(initial_image)

        # 提取动作名称和存储详细信息
        self.actions = [action["name"] for action in vision_guided_actions]
        self.action_details = {action["name"]: action for action in vision_guided_actions}

        logger.info(f"🌳 Starting Vision-Guided Agent-R MCTS search: {self.max_iterations} iterations")
        logger.info(f"📊 Generated {len(self.actions)} vision-guided actions:")
        for action_name in self.actions:
            action_info = self.action_details[action_name]
            priority = action_info.get('priority', 'medium')
            reason = action_info.get('reason', 'N/A')
            logger.info(f"  🎯 {action_name} ({action_info['category']}, {priority}): {action_info['description']}")
            logger.info(f"     💡 Reason: {reason}")
        logger.info(f"🎯 Max depth: {self.max_depth}, Rollback threshold: {self.rollback_threshold}")

        for iteration in range(self.max_iterations):
            logger.info(f"\n--- 🔄 MCTS Iteration {iteration + 1}/{self.max_iterations} ---")

            # Phase 1: Selection with rollback capability
            selected_node = self._select_with_rollback(root)
            logger.info(f"🎯 Selected node: {selected_node.node_id} (depth: {selected_node.depth}, visits: {selected_node.visits}, avg_reward: {selected_node.average_reward:.3f})")

            # Phase 2: Expansion
            expanded_node = self._expand(selected_node)
            if expanded_node != selected_node:
                logger.info(f"🌱 Expanded new node: {expanded_node.node_id} with action: {expanded_node.action}")
            else:
                logger.info(f"⚠️  No expansion possible for node: {selected_node.node_id}")

            # Phase 3: Simulation
            reward = self._simulate(expanded_node, evaluator)
            logger.info(f"🎲 Simulation reward: {reward:.3f} for node: {expanded_node.node_id}")

            # Phase 4: Backpropagation with rollback check
            rollback_occurred = self._backpropagate_with_rollback(expanded_node, reward)
            if rollback_occurred:
                logger.info(f"🔄 Rollback occurred during backpropagation")

            # 显示当前树状态
            if iteration % 10 == 0:  # 每10次迭代显示树状态
                self._print_tree_status(root, iteration)
                trajectory = self._extract_best_trajectory(root, f"traj_{iteration}")
                if trajectory:
                    trajectories.append(trajectory)
                    logger.info(f"✅ Collected trajectory: {trajectory.trajectory_id} (reward: {trajectory.total_reward:.3f}, length: {trajectory.length})")
        
        # 最终轨迹收集
        final_trajectories = self._collect_all_trajectories(root)
        trajectories.extend(final_trajectories)

        # 显示最终树结构
        logger.info(f"\n🏁 Final MCTS Tree Structure:")
        self._print_final_tree(root)

        logger.info(f"🎉 MCTS completed: {len(trajectories)} trajectories, "
                   f"{self.stats['total_rollbacks']} rollbacks")

        return trajectories
    
    def _select_with_rollback(self, root: MCTSNode) -> MCTSNode:
        """带回滚机制的选择阶段"""
        current = root
        
        while not current.is_leaf and current.depth < self.max_depth:
            # 检查是否需要回滚
            if current.average_reward < self.rollback_threshold and current.parent:
                logger.debug(f"Triggering rollback at node {current.node_id}")
                rolled_back = current.rollback_to_parent()
                if rolled_back:
                    self.stats['total_rollbacks'] += 1
                    current = rolled_back
                    continue
            
            # UCB1 选择
            if current.children:
                current = max(current.children, key=lambda n: n.ucb1_score())
            else:
                break
        
        return current
    
    def _expand(self, node: MCTSNode) -> MCTSNode:
        """扩展节点并应用图像操作"""
        if node.depth >= self.max_depth:
            return node

        # 选择一个未尝试的动作
        untried_actions = [a for a in self.actions
                          if not any(child.action == a for child in node.children)]

        logger.info(f"🔍 Node {node.node_id}: {len(self.actions)} total actions, {len(untried_actions)} untried")
        if len(self.actions) <= 3:  # 如果动作太少，显示详细信息
            logger.info(f"  Total actions: {self.actions}")
            logger.info(f"  Tried actions: {[child.action for child in node.children]}")
            logger.info(f"  Untried actions: {untried_actions}")

        if not untried_actions:
            logger.info(f"⚠️  No untried actions for node {node.node_id}")
            return node

        action = random.choice(untried_actions)
        logger.info(f"🎲 Selected action: {action} from {len(untried_actions)} untried actions")
        child_id = f"{node.node_id}_{action}_{len(node.children)}"

        # 应用图像操作
        processed_image = self._apply_action(node.image_state, action) if node.image_state else None

        child = MCTSNode(
            node_id=child_id,
            action=action,
            parent=node,
            image_state=processed_image,
            depth=node.depth + 1
        )

        node.children.append(child)
        return child

    def _apply_action(self, image: Image.Image, action: str) -> Image.Image:
        """应用智能图像操作"""
        if not image:
            return image

        try:
            # 获取动作的详细信息
            action_info = self.action_details.get(action, {
                "name": action,
                "description": f"Unknown action: {action}",
                "category": "general"
            })

            logger.info(f"🎨 Applying intelligent action: {action}")
            logger.info(f"  📝 {action_info['description']}")
            logger.info(f"  🏷️  Category: {action_info['category']}")

            # 使用智能图像处理器应用动作
            result = self.image_processor.apply_intelligent_action(image, action_info)

            return result

        except Exception as e:
            logger.warning(f"❌ Failed to apply intelligent action {action}: {e}")
            return image

    def _simulate(self, node: MCTSNode, evaluator: Callable) -> float:
        """模拟执行"""
        if not node.image_state or not node.action:
            return 0.0
        
        try:
            # 简化的图像评估
            reward = evaluator(node.image_state, node.action)
            return max(-1.0, min(1.0, reward))  # 限制在 [-1, 1] 范围
        except Exception as e:
            logger.warning(f"Simulation failed for node {node.node_id}: {e}")
            return -0.5
    
    def _backpropagate_with_rollback(self, node: MCTSNode, reward: float) -> bool:
        """带回滚检查的反向传播"""
        current = node
        rollback_occurred = False

        while current is not None:
            current.visits += 1
            current.total_reward += reward

            # 检查是否需要回滚（基于累积奖励）
            if (current.visits > 5 and
                current.average_reward < self.rollback_threshold and
                current.parent is not None):

                # 执行回滚
                parent = current.rollback_to_parent()
                if parent:
                    self.stats['total_rollbacks'] += 1
                    rollback_occurred = True
                    logger.info(f"🔄 Rollback executed: {current.node_id} -> {parent.node_id} (avg_reward: {current.average_reward:.3f})")
                    current = parent
                    continue

            current = current.parent

        return rollback_occurred

    def _print_tree_status(self, root: MCTSNode, iteration: int):
        """打印当前树的状态"""
        logger.info(f"\n🌳 Tree Status at Iteration {iteration}:")
        logger.info(f"📊 Root node visits: {root.visits}, avg_reward: {root.average_reward:.3f}")

        # 统计树的基本信息
        total_nodes = self._count_nodes(root)
        max_depth = self._get_max_depth(root)

        logger.info(f"📈 Total nodes: {total_nodes}, Max depth: {max_depth}")

        # 显示最佳路径
        best_path = self._get_best_path(root)
        if best_path:
            path_str = " -> ".join([f"{node.node_id}({node.action or 'root'})" for node in best_path])
            logger.info(f"🎯 Best path: {path_str}")

        # 显示每层的节点统计
        self._print_depth_stats(root)

    def _count_nodes(self, node: MCTSNode) -> int:
        """递归计算节点总数"""
        count = 1
        for child in node.children:
            count += self._count_nodes(child)
        return count

    def _get_max_depth(self, node: MCTSNode) -> int:
        """获取树的最大深度"""
        if not node.children:
            return node.depth
        return max(self._get_max_depth(child) for child in node.children)

    def _get_best_path(self, root: MCTSNode) -> List[MCTSNode]:
        """获取当前最佳路径"""
        path = []
        current = root

        while current:
            path.append(current)
            if not current.children:
                break
            # 选择平均奖励最高的子节点
            current = max(current.children, key=lambda n: n.average_reward)

        return path

    def _print_depth_stats(self, root: MCTSNode):
        """打印每个深度的统计信息"""
        depth_stats = {}
        self._collect_depth_stats(root, depth_stats)

        for depth in sorted(depth_stats.keys()):
            nodes = depth_stats[depth]
            avg_reward = sum(n.average_reward for n in nodes) / len(nodes)
            total_visits = sum(n.visits for n in nodes)
            logger.info(f"  Depth {depth}: {len(nodes)} nodes, avg_reward: {avg_reward:.3f}, total_visits: {total_visits}")

    def _collect_depth_stats(self, node: MCTSNode, depth_stats: dict):
        """递归收集深度统计信息"""
        if node.depth not in depth_stats:
            depth_stats[node.depth] = []
        depth_stats[node.depth].append(node)

        for child in node.children:
            self._collect_depth_stats(child, depth_stats)

    def _print_final_tree(self, root: MCTSNode):
        """打印最终的完整树结构"""
        logger.info(f"🌳 Complete Tree Structure:")
        self._print_node_recursive(root, "", True)

        # 统计最终信息
        total_nodes = self._count_nodes(root)
        max_depth = self._get_max_depth(root)
        best_path = self._get_best_path(root)

        logger.info(f"\n📊 Final Tree Statistics:")
        logger.info(f"  Total nodes: {total_nodes}")
        logger.info(f"  Maximum depth: {max_depth}")
        logger.info(f"  Total rollbacks: {self.stats['total_rollbacks']}")

        if best_path:
            best_reward = best_path[-1].average_reward if best_path else 0
            logger.info(f"  Best path length: {len(best_path)}")
            logger.info(f"  Best path reward: {best_reward:.3f}")

    def _print_node_recursive(self, node: MCTSNode, prefix: str, is_last: bool):
        """递归打印节点树结构"""
        # 节点信息
        action_str = f"[{node.action}]" if node.action else "[ROOT]"
        reward_str = f"R:{node.average_reward:.3f}" if node.visits > 0 else "R:N/A"
        visits_str = f"V:{node.visits}"

        connector = "└── " if is_last else "├── "
        logger.info(f"{prefix}{connector}{node.node_id} {action_str} {reward_str} {visits_str}")

        # 递归打印子节点
        if node.children:
            extension = "    " if is_last else "│   "
            for i, child in enumerate(node.children):
                is_child_last = (i == len(node.children) - 1)
                self._print_node_recursive(child, prefix + extension, is_child_last)

    def _extract_best_trajectory(self, root: MCTSNode, traj_id: str) -> Optional[Trajectory]:
        """提取最佳轨迹"""
        trajectory = Trajectory(trajectory_id=traj_id)
        current = root
        
        # 沿着最佳路径构建轨迹
        while current.children:
            trajectory.add_node(current)
            current = max(current.children, key=lambda n: n.average_reward)
        
        trajectory.add_node(current)  # 添加叶节点
        
        # 判断轨迹成功性
        trajectory.success = trajectory.total_reward > 0
        if trajectory.success:
            self.stats['successful_trajectories'] += 1
        else:
            self.stats['failed_trajectories'] += 1
        
        return trajectory if trajectory.length > 1 else None
    
    def _collect_all_trajectories(self, root: MCTSNode) -> List[Trajectory]:
        """收集所有有效轨迹"""
        trajectories = []
        
        def dfs_collect(node: MCTSNode, path: List[MCTSNode]):
            path.append(node)
            
            if node.is_leaf and len(path) > 1:
                # 创建轨迹
                traj = Trajectory(trajectory_id=f"final_{len(trajectories)}")
                for n in path:
                    traj.add_node(n)
                traj.success = traj.total_reward > 0
                trajectories.append(traj)
            
            for child in node.children:
                dfs_collect(child, path.copy())
        
        dfs_collect(root, [])
        return trajectories[:10]  # 限制轨迹数量
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取搜索统计信息"""
        return self.stats.copy()


def create_simple_evaluator(aesthetic_evaluator) -> Callable:
    """创建简化的评估函数"""
    def evaluate(image: Image.Image, action: str) -> float:
        try:
            if aesthetic_evaluator:
                # 使用正确的方法名和返回值
                scores = aesthetic_evaluator.evaluate(image)
                combined_score = scores.get('combined', 0.5)
                reward = (combined_score - 0.5) * 2  # 转换到 [-1, 1] 范围

                # 详细的评估信息
                logger.info(f"📊 Evaluation results for action '{action}':")
                for model_name, score in scores.items():
                    if model_name != 'combined':
                        logger.info(f"  {model_name}: {score:.3f}")
                logger.info(f"  Combined score: {combined_score:.3f} -> Reward: {reward:.3f}")

                return reward
            else:
                # 简单的随机评估（用于测试）
                reward = random.uniform(-0.5, 0.5)
                logger.info(f"📊 Random evaluation for action '{action}': {reward:.3f}")
                return reward
        except Exception as e:
            logger.warning(f"❌ Evaluation failed for action '{action}': {e}")
            return 0.0

    return evaluate
