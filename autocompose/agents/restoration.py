"""
Unified Restoration Agent Module

Implements comprehensive image restoration capabilities including:
- Traditional image operations (crop, rotate, color adjust, etc.)
- MLLM-based editing (Flux.1, Bagel, InstructPix2Pix, GPT-4+DALL-E)
- Rollback and quality monitoring
- Session management and edit history
"""

# 设置 CUDA 确定性行为的环境变量
import os
if 'CUBLAS_WORKSPACE_CONFIG' not in os.environ:  # 检查环境变量是否已设置
    os.environ['CUBLAS_WORKSPACE_CONFIG'] = ':4096:8'  # 设置CUDA工作空间配置，确保结果可重现

# 过度的架构文档已删除，保留核心功能



import json  # 用于JSON数据处理
import logging  # 日志记录模块
import time  # 时间相关操作
import base64  # Base64编码解码
import io  # 输入输出流操作
import copy  # 深拷贝对象
import numpy as np  # 数值计算库
from typing import Dict, List, Any, Optional, Union, Tuple  # 类型提示
from PIL import Image  # 图像处理库
from dataclasses import dataclass, field  # 数据类装饰器

# 导入项目内部模块
from ..core.profile import Profile  # 配置文件管理
from ..core.evaluator import AestheticEvaluator  # 美学评估器
from ..utils.image_ops import ImageOperations  # 图像操作工具
from .planning import AdjustmentPlan, EditingInstruction  # 规划相关类
from .perception import PerceptionAgent  # 感知代理
from .unified_types import PerceptionResult  # 统一类型定义
# 简化控制器 - 移除复杂的规划链控制
# from .master_controller import MasterController, ControllerDecision, ControlDecision  # 主控制器

logger = logging.getLogger(__name__)  # 获取当前模块的日志记录器


# 导入统一的结果类
from .unified_types import OperationResult, BatchResult

# 向后兼容别名已删除，直接使用原始类名


class RestorationAgent:
    """
    Unified Restoration Agent supporting both traditional operations and MLLM editing
    统一的修复代理，支持传统操作和MLLM编辑
    """

    def __init__(self, profile: Profile):
        """
        Initialize Restoration Agent with traditional and MLLM capabilities
        使用传统和MLLM功能初始化修复代理

        Args:
            profile: System configuration profile - 系统配置文件
        """
        self.profile = profile  # 保存配置文件
        self.aesthetic_evaluator = None  # 美学评估器初始化为空
        self.perception_agent = None  # 感知代理初始化为空
        self.mllm_models = {}  # MLLM模型字典初始化为空
        self.controller = None  # 控制器初始化为空

        # Configuration - 配置项
        # 检查是否启用MLLM编辑功能
        self.enable_mllm_editing = getattr(profile.profile, 'enable_mllm_restoration', False) if profile.profile else False
        # 检查是否启用自动回滚功能
        self.enable_rollback = getattr(profile.mllm_restoration, 'enable_automatic_rollback', True) if hasattr(profile, 'mllm_restoration') and profile.mllm_restoration else True
        # 检查是否启用规划链功能
        self.enable_planning_chain = getattr(profile.profile, 'enable_planning_chain', False) if profile.profile else False

        # Initialize components - 初始化组件
        self._initialize_components()  # 初始化基础组件
        self._initialize_mllm_models()  # 初始化MLLM模型

        # 记录初始化信息
        logger.info(f"Restoration agent initialized with MLLM editing: {'✓' if self.enable_mllm_editing else '✗'}")
        logger.info(f"Planning Chain enabled: {'✓' if self.enable_planning_chain else '✗'}")
        if self.enable_mllm_editing:  # 如果启用了MLLM编辑
            logger.info(f"Available MLLM models: {list(self.mllm_models.keys())}")  # 记录可用模型

    def _initialize_components(self):
        """Initialize aesthetic evaluator and perception agent - 初始化美学评估器和感知代理"""
        if self.profile.profile:  # 如果配置文件存在
            # Initialize aesthetic evaluator - 初始化美学评估器
            aesthetic_models = self.profile.profile.aesthetic_models  # 获取美学模型配置
            device = self.profile.profile.device  # 获取设备配置
            random_seed = getattr(self.profile.profile, 'random_seed', 42)  # 获取随机种子，默认42
            self.aesthetic_evaluator = AestheticEvaluator(aesthetic_models, device, random_seed)  # 创建美学评估器
            logger.info(f"Aesthetic evaluator initialized with random seed: {random_seed}")
            # Initialize perception agent for reflection - 初始化用于反思的感知代理
            self.perception_agent = PerceptionAgent(self.profile)

    def _reflect_on_result(self, execution_result: OperationResult, rollback_threshold: float) -> Tuple[bool, str]:
        """
        Reflect on execution result to decide whether to keep or rollback
        反思执行结果以决定是否保留或回滚

        Args:
            execution_result: Result of the executed task - 执行任务的结果
            rollback_threshold: Minimum improvement threshold - 最小改进阈值

        Returns:
            Tuple of (should_keep, reason) - 返回(是否保留, 原因)的元组
        """
        improvement = execution_result.improvement  # 获取改进值
        if improvement >= rollback_threshold:  # 如果改进值大于等于阈值
            return True, f"Good improvement: {improvement:.3f} >= {rollback_threshold:.3f}"  # 保留，因为改进显著
        elif improvement > 0:  # 如果改进值大于0但小于阈值
            return True, f"Small improvement: {improvement:.3f} (keeping anyway)"  # 仍然保留，因为有改进
        else:  # 如果没有改进或恶化
            return False, f"No improvement or degradation: {improvement:.3f}"  # 回滚

    def _initialize_mllm_models(self):
        """Initialize MLLM models based on configuration - 根据配置初始化MLLM模型"""
        if not self.enable_mllm_editing:  # 如果没有启用MLLM编辑
            logger.info("MLLM editing disabled, skipping model initialization")  # 跳过模型初始化
            return

        initialized_models = []  # 已初始化的模型列表
        failed_models = []  # 初始化失败的模型列表

        try:
            # Get model priority from config - 从配置获取模型优先级
            if hasattr(self.profile, 'mllm_restoration') and self.profile.mllm_restoration:
                model_priority = getattr(self.profile.mllm_restoration, 'model_priority', ["flux"])  # 获取模型优先级
            else:
                model_priority = ["flux"]  # 默认使用flux模型

            logger.info(f"Initializing MLLM models in priority order: {model_priority}")

            # Initialize models in priority order - 按优先级顺序初始化模型
            for model_name in model_priority:
                try:
                    init_method = getattr(self, f"_init_{model_name}_model", None)  # 获取模型初始化方法
                    if init_method:  # 如果方法存在
                        init_method()  # 调用初始化方法
                        initialized_models.append(model_name)  # 添加到已初始化列表
                        logger.info(f"✓ Successfully initialized {model_name}")  # 记录成功
                    else:
                        logger.warning(f"Unknown model type: {model_name}")  # 记录未知模型类型
                except Exception as e:
                    logger.warning(f"✗ Failed to initialize {model_name}: {e}")  # 记录初始化失败
                    failed_models.append((model_name, str(e)))  # 添加到失败列表

            # Ensure at least one model is available - 确保至少有一个模型可用
            if not initialized_models:  # 如果没有成功初始化的模型
                logger.warning("No MLLM models successfully initialized. MLLM editing will be disabled.")
                self.enable_mllm_editing = False  # 禁用MLLM编辑
            else:
                logger.info(f"MLLM editing enabled with {len(initialized_models)} model(s)")  # 记录启用的模型数量

        except Exception as e:
            logger.error(f"Critical error during MLLM models initialization: {e}")  # 记录关键错误
            self.mllm_models = {}  # 清空模型字典
            self.enable_mllm_editing = False  # 禁用MLLM编辑

    def _init_flux_model(self):
        """Initialize Flux.1 Kontext Dev model - 初始化Flux.1 Kontext Dev模型"""
        try:
            from diffusers import FluxKontextPipeline  # 导入Flux管道
            import torch  # 导入PyTorch
            
            # Load model config - 加载模型配置
            model_config = getattr(self.profile.mllm_restoration, 'flux', {}) if hasattr(self.profile, 'mllm_restoration') and self.profile.mllm_restoration else {}
            model_id = model_config.get('model_id', 'black-forest-labs/FLUX.1-Kontext-dev')  # 获取模型ID
            # Load model pipeline - 加载模型管道
            pipe = FluxKontextPipeline.from_pretrained(
                model_id,
                torch_dtype=torch.bfloat16,  # 使用bfloat16精度
                # trust_remote_code=True,
                # low_cpu_mem_usage=True
            )
            
            # Set device - 设置设备
            device = self.profile.profile.device if self.profile.profile else "cuda"  # 获取设备配置
            if device == "cuda" and torch.cuda.is_available():  # 如果是CUDA且可用
                pipe = pipe.to("cuda")  # 移动到CUDA
            elif device == "cpu":  # 如果是CPU
                pipe = pipe.to("cpu")  # 移动到CPU
            
            # Store model - 存储模型
            self.mllm_models['flux'] = {
                'pipeline': pipe,  # 存储管道
                'config': model_config,  # 存储配置
                'type': 'diffusion'  # 标记类型为扩散模型
            }
            logger.info(f"Flux.1 Kontext Dev model initialized successfully on {device}")
            
        except Exception as e:
            logger.error(f"Failed to initialize Flux.1: {e}")  # 记录初始化失败
            raise

    def _init_bagel_model(self):
        """Initialize Bagel model - using InstructPix2Pix as base - 初始化Bagel模型（基于InstructPix2Pix）"""
        try:
            from diffusers import StableDiffusionInstructPix2PixPipeline  # 导入InstructPix2Pix管道
            import torch

            # 获取Bagel模型配置
            model_config = getattr(self.profile.mllm_restoration, 'bagel', {}) if hasattr(self.profile, 'mllm_restoration') and self.profile.mllm_restoration else {}
            model_id = model_config.get('model_id', 'timbrooks/instruct-pix2pix')  # 获取模型ID

            # 创建管道
            pipe = StableDiffusionInstructPix2PixPipeline.from_pretrained(
                model_id,
                torch_dtype=torch.bfloat16,  # 使用bfloat16精度
                device_map="balanced",  # 平衡设备映射
                safety_checker=None,  # 禁用安全检查器
                requires_safety_checker=False,  # 不需要安全检查器
                trust_remote_code=True  # 信任远程代码
            )

            # 设置设备
            device = self.profile.profile.device if self.profile.profile else "cuda"
            if device == "cuda" and torch.cuda.is_available():
                pipe = pipe.to(device)
            elif device == "cpu":
                pipe = pipe.to("cpu")

            # 启用优化
            if hasattr(pipe, 'enable_attention_slicing'):  # 如果支持注意力切片
                pipe.enable_attention_slicing()  # 启用注意力切片以节省内存
            if hasattr(pipe, 'enable_model_cpu_offload'):  # 如果支持CPU卸载
                pipe.enable_model_cpu_offload()  # 启用模型CPU卸载

            # 存储模型
            self.mllm_models['bagel'] = {
                'pipeline': pipe,
                'config': model_config,
                'type': 'diffusion'
            }
            
        except Exception as e:
            logger.error(f"Failed to initialize Bagel: {e}")
            raise

    # ...existing code for other model initialization methods...

    def execute_plan(self, image: Union[Image.Image, str], plan: AdjustmentPlan,
                     perception_result: Optional[PerceptionResult] = None,
                     planner = None) -> BatchResult:
        """
        Execute complete adjustment plan with reflection and rollback
        执行完整的调整计划，包含反思和回滚功能
        Enhanced with MLLM-based editing capabilities and Planning Chain support
        增强了MLLM编辑功能和规划链支持

        Args:
            image: Input image (PIL Image or path) - 输入图像（PIL图像或路径）
            plan: Adjustment plan to execute - 要执行的调整计划
            perception_result: Original perception result (for Planning Chain) - 原始感知结果（用于规划链）
            planner: Planning Chain planner (for replanning) - 规划链规划器（用于重新规划）

        Returns:
            Complete restoration result - 完整的修复结果
        """
        start_time = time.time()  # 记录开始时间

        # Load image if path provided - 如果提供的是路径则加载图像
        if isinstance(image, str):  # 如果输入是字符串（路径）
            original_image = Image.open(image).convert("RGB")  # 打开图像并转换为RGB
        else:
            original_image = image.copy()  # 否则复制图像

        # Initialize Agent-R trajectory collection
        trajectory_collector = None
        try:
            from .trajectory import TrajectoryCollector, TrajectoryStatus
            trajectory_collector = TrajectoryCollector()
            image_path = str(image) if isinstance(image, str) else "memory_image"
            trajectory_id = trajectory_collector.start_trajectory(image_path)
            logger.info(f"Started Agent-R trajectory collection: {trajectory_id}")
        except Exception as e:
            logger.warning(f"Failed to initialize trajectory collection: {e}")

        # Check if Planning Chain is enabled and we have the required components
        # 检查是否启用了规划链且有必需的组件
        if (self.enable_planning_chain and perception_result is not None and
            planner is not None):
            logger.info("Planning Chain enabled, using enhanced execution with feedback control")
            result = self._execute_plan_with_planning_chain(original_image, plan, perception_result, planner, trajectory_collector)
        else:
            logger.info("Using standard MLLM execution")  # 使用标准MLLM执行
            result = self._execute_plan_with_mllm(original_image, plan, trajectory_collector)

        # Complete trajectory collection
        if trajectory_collector:
            try:
                status = TrajectoryStatus.SUCCESS if result.success else TrajectoryStatus.FAILURE
                trajectory_collector.complete_trajectory(status)
                logger.info("Completed trajectory collection")
            except Exception as e:
                logger.warning(f"Failed to complete trajectory collection: {e}")

        return result

    def _execute_plan_simplified(self, image: Image.Image, plan: AdjustmentPlan) -> BatchResult:
        """简化的计划执行 - 移除复杂控制逻辑"""
        start_time = time.time()
        current_image = image.copy()
        applied_tasks = []
        initial_score = self._get_aesthetic_score(image)
        best_image = image.copy()
        best_score = initial_score

        logger.info(f"Starting simplified execution: {len(plan.instructions)} steps")

        for i, instruction in enumerate(plan.instructions):
            logger.info(f"Executing step {i+1}/{len(plan.instructions)}: {instruction.instruction}")

            # 执行步骤
            step_result = self._execute_single_step(current_image, instruction)

            # 简单的成功判断
            if step_result.success and step_result.output_image:
                current_image = step_result.output_image
                if step_result.score_after > best_score:
                    best_image = step_result.output_image.copy()
                    best_score = step_result.score_after

            applied_tasks.append(step_result)

        # 计算最终结果
        final_score = self._get_aesthetic_score(best_image)
        total_improvement = final_score - initial_score
        processing_time = time.time() - start_time

        return BatchResult(
            success=len([t for t in applied_tasks if t.success]) > 0,
            message=f"Simplified execution completed: {len(applied_tasks)} steps",
            final_image=best_image,
            original_score=initial_score,
            final_score=final_score,
            improvement=total_improvement,
            processing_time=processing_time,
            applied_operations=applied_tasks
        )

    def _execute_plan_with_planning_chain(self, image: Image.Image, plan: AdjustmentPlan,
                                        perception_result: PerceptionResult,
                                        planner) -> BatchResult:
        """Execute plan using Planning Chain with feedback and rollback control
        使用规划链执行计划，具备反馈和回滚控制"""
        start_time = time.time()

        # 确保输入图像是RGB格式
        if image.mode != 'RGB':
            image = image.convert('RGB')

        # 简化执行 - 移除复杂控制器
        # self.controller = MasterController(self.profile)
        # self.controller.start_sequential_execution(plan)  # 开始顺序执行

        # 初始化跟踪变量
        current_image = image.copy()  # 当前图像
        applied_tasks = []  # 已应用的任务列表
        current_plan = plan  # 当前计划
        initial_score = self._get_aesthetic_score(image)  # 初始美学评分
        best_image = image.copy()  # 最佳图像
        best_score = initial_score  # 最佳评分

        logger.info(f"Starting Planning Chain execution with {len(plan.instructions)} instructions")
        logger.info(f"Initial aesthetic score: {initial_score:.3f}")

        step_index = 0  # 步骤索引
        max_total_steps = len(plan.instructions) * 3  # 防止无限循环的最大步数
        total_steps_executed = 0  # 已执行的总步数

        # 主执行循环
        while step_index < len(current_plan.instructions) and total_steps_executed < max_total_steps:
            if not self.controller.should_continue():  # 检查是否应该继续
                logger.warning("Controller decided to stop execution")
                break

            instruction = current_plan.instructions[step_index]  # 获取当前指令
            logger.info(f"Executing step {step_index + 1}: {instruction.instruction}")

            # 执行当前步骤
            step_result = self._execute_single_step_with_planning_chain(current_image, instruction)
            total_steps_executed += 1  # 增加执行步数计数

            # 让控制器评估结果
            decision = self.controller.evaluate_step(step_result, instruction)
            logger.info(f"Controller decision: {decision.strategy} - {decision.reason}")

            # 根据决策策略处理结果
            if decision.strategy == ControlDecision.CONTINUE.value:  # 继续策略
                # 继续执行，接受当前结果
                if step_result.success and step_result.output_image:
                    current_image = step_result.output_image  # 更新当前图像
                    if step_result.score_after > best_score:  # 如果评分更好
                        best_image = step_result.output_image.copy()  # 更新最佳图像
                        best_score = step_result.score_after  # 更新最佳评分

                applied_tasks.append(step_result)  # 添加到已应用任务
                step_index += 1  # 移动到下一步

            elif decision.strategy == ControlDecision.RETRY.value:  # 重试策略
                # 重试当前步骤，不增加 step_index
                logger.info(f"Retrying step {step_index}")
                # 不添加到 applied_tasks，不增加 step_index

            elif decision.strategy == ControlDecision.ABORT.value:  # 中止策略
                # 中止执行
                logger.warning(f"Execution aborted: {decision.reason}")
                abort_result = OperationResult(
                    success=False,
                    message=f"Execution aborted: {decision.reason}",
                    instruction=f"ABORTED: {instruction.instruction}",
                    input_image=current_image,
                    output_image=current_image,
                    score_before=step_result.score_before,
                    score_after=step_result.score_before,
                    improvement=0.0,
                    processing_time=0.0
                )
                applied_tasks.append(abort_result)
                break  # 跳出执行循环

        # 计算最终指标
        total_time = time.time() - start_time  # 总执行时间
        successful_tasks = sum(1 for result in applied_tasks if result.success)  # 成功任务数
        success_rate = successful_tasks / len(applied_tasks) if applied_tasks else 0  # 成功率

        final_score = self._get_aesthetic_score(best_image)  # 最终评分
        total_improvement = final_score - initial_score  # 总改进值

        # 获取执行摘要
        execution_summary = self.controller.get_execution_summary()

        # 记录执行完成信息
        logger.info(f"Planning Chain execution complete:")
        logger.info(f"  Initial score: {initial_score:.3f}, Final score: {final_score:.3f}")
        logger.info(f"  Total improvement: {total_improvement:.3f}")
        logger.info(f"  Steps executed: {total_steps_executed}, Rollbacks: {execution_summary['rollback_counts']}")

        # 返回批量操作结果
        return BatchResult(
            success=success_rate > 0,
            processing_time=total_time,
            original_image=image,
            final_image=best_image,
            operation_results=applied_tasks,
            total_improvement=total_improvement,
            final_score=final_score,
            original_score=initial_score
        )

    def _execute_single_step_with_planning_chain(self, image: Image.Image,
                                               instruction: EditingInstruction) -> OperationResult:
        """执行单个步骤（Planning Chain 版本）"""
        start_time = time.time()  # 记录开始时间

        try:
            # 确保输入图像是RGB格式
            if image.mode != 'RGB':
                image = image.convert('RGB')

            # 获取执行前的评分
            score_before = self._get_aesthetic_score(image)

            # 尝试使用可用的模型执行编辑
            edited_image = None  # 编辑后的图像
            model_used = None  # 使用的模型

            # 按优先级顺序尝试各个模型
            for model_name in self.mllm_models.keys():
                try:
                    editing_result = self._execute_mllm_edit(image, instruction.instruction, model_name)
                    if editing_result.success and editing_result.output_image:  # 如果编辑成功
                        edited_image = editing_result.output_image
                        model_used = model_name
                        break  # 成功后跳出循环
                except Exception as e:
                    logger.warning(f"Model {model_name} failed: {e}")  # 记录模型失败
                    continue  # 尝试下一个模型

            processing_time = time.time() - start_time  # 计算处理时间

            if edited_image is not None:  # 如果编辑成功
                # 成功编辑
                score_after = self._get_aesthetic_score(edited_image)  # 获取编辑后评分
                improvement = score_after - score_before  # 计算改进值

                return OperationResult(
                    success=True,
                    instruction=instruction.instruction,
                    input_image=image,
                    output_image=edited_image,
                    score_before=score_before,
                    score_after=score_after,
                    improvement=improvement,
                    processing_time=processing_time,
                    model_used=model_used
                )
            else:
                # 编辑失败
                return OperationResult(
                    success=False,
                    message="No available model could process this instruction",
                    instruction=instruction.instruction,
                    input_image=image,
                    output_image=image,
                    score_before=score_before,
                    score_after=score_before,
                    improvement=0.0,
                    processing_time=processing_time
                )

        except Exception as e:
            processing_time = time.time() - start_time
            score_before = self._get_aesthetic_score(image)

            return OperationResult(
                success=False,
                message=str(e),
                instruction=instruction.instruction,
                input_image=image,
                output_image=image,  # 失败操作使用原始图像
                score_before=score_before,
                score_after=score_before,  # 失败操作不改变评分
                improvement=0.0,
                processing_time=processing_time
            )

    def _execute_mllm_edit(self, image: Image.Image, instruction: str, model_name: str) -> OperationResult:
        """Execute single MLLM edit operation - 执行单个MLLM编辑操作"""
        start_time = time.time()

        try:
            # 确保输入图像是RGB格式
            if image.mode != 'RGB':
                image = image.convert('RGB')
                
            if model_name not in self.mllm_models:  # 检查模型是否可用
                raise ValueError(f"Model {model_name} not available")

            model_info = self.mllm_models[model_name]  # 获取模型信息
            edit_method = getattr(self, f"_edit_with_{model_name}", None)  # 获取编辑方法
            if not edit_method:  # 如果方法不存在
                raise ValueError(f"No edit method for model {model_name}")
            
            result_image = edit_method(image, instruction, model_info)  # 执行编辑
            
            # 确保结果图像是RGB格式
            if result_image.mode != 'RGB':
                result_image = result_image.convert('RGB')

            processing_time = time.time() - start_time  # 计算处理时间

            # Calculate scores for the result - 计算结果的评分
            score_before = self._get_aesthetic_score(image)  # 编辑前评分
            score_after = self._get_aesthetic_score(result_image)  # 编辑后评分
            improvement = score_after - score_before  # 改进值

            return OperationResult(
                success=True,
                instruction=instruction,
                input_image=image,
                output_image=result_image,
                score_before=score_before,
                score_after=score_after,
                improvement=improvement,
                processing_time=processing_time,
                model_used=model_name,
                # confidence=0.8,  # TODO: Implement confidence scoring
                # metadata={"model_config": model_info.get('config', {})}
            )

        except Exception as e:
            processing_time = time.time() - start_time
            logger.error(f"MLLM edit failed with {model_name}: {e}")

            # For failed operations, use original image and zero scores
            # 对于失败的操作，使用原始图像和零评分
            score_before = self._get_aesthetic_score(image)

            return OperationResult(
                success=False,
                message=str(e),
                instruction=instruction,
                input_image=image,
                output_image=image,  # 失败操作使用原始图像
                score_before=score_before,
                score_after=score_before,  # 失败操作不改变评分
                improvement=0.0,
                processing_time=processing_time,
                model_used=model_name
            )

    def _pad_to_square_flux(self, image: Image.Image, target_size: int = 1024) -> tuple[Image.Image, tuple]:
        """
        将图像填充到正方形，保持原始内容完整 - 专为Flux模型设计
        Pad image to square while preserving original content - designed for Flux model
        """
        width, height = image.size

        # 如果已经是正方形且尺寸合适，直接返回
        if width == height == target_size:
            return image, (0, 0, width, height)

        # 计算缩放比例，确保图像能完全放入目标尺寸
        scale = min(target_size / width, target_size / height)
        new_width = int(width * scale)
        new_height = int(height * scale)

        # 缩放图像
        scaled_image = image.resize((new_width, new_height), Image.Resampling.LANCZOS)

        # 创建正方形画布 - 使用中性灰色背景
        padded_image = Image.new('RGB', (target_size, target_size), (128, 128, 128))

        # 计算居中位置
        x_offset = (target_size - new_width) // 2
        y_offset = (target_size - new_height) // 2

        # 将缩放后的图像粘贴到画布中心
        padded_image.paste(scaled_image, (x_offset, y_offset))

        # 返回填充后的图像和原始内容区域坐标
        return padded_image, (x_offset, y_offset, x_offset + new_width, y_offset + new_height)

    def _extract_from_square_flux(self, image: Image.Image, content_area: tuple, target_size: tuple) -> Image.Image:
        """
        从正方形图像中提取原始内容区域并调整到目标尺寸
        Extract original content area from square image and resize to target size
        """
        x1, y1, x2, y2 = content_area

        # 提取内容区域
        content_image = image.crop((x1, y1, x2, y2))

        # 调整到目标尺寸
        return content_image.resize(target_size, Image.Resampling.LANCZOS)

    def _edit_with_flux(self, image: Image.Image, instruction: str, model_info: Dict) -> Image.Image:
        """Edit image using Flux.1 Kontext Dev model - 解决裁剪问题的版本"""
        pipeline = model_info['pipeline']  # 获取管道
        config = model_info.get('config', {})  # 获取配置

        try:
            # Ensure image is in RGB format - 确保图像是RGB格式
            if image.mode != 'RGB':
                image = image.convert('RGB')

            original_size = image.size
            logger.debug(f"Original Flux input image size: {original_size}")

            # 使用填充方案避免裁剪问题 - Use padding approach to avoid cropping
            target_size = config.get('max_image_size', 1024)
            padded_image, content_area = self._pad_to_square_flux(image, target_size)
            logger.debug(f"Padded to square: {padded_image.size}, content area: {content_area}")

            # Create prompt - 创建提示
            enhanced_prompt = f"Edit this image: {instruction}. "  # 增强的提示文本

            # 使用管道生成结果 - 不传入width/height参数，让模型使用默认的1024x1024
            result = pipeline(
                image=padded_image,
                prompt=enhanced_prompt,
                guidance_scale=config.get('guidance_scale', 5),  # 引导强度
                # 不设置width和height，让Flux模型使用默认的1024x1024
                num_inference_steps=config.get('num_inference_steps', 20),  # 推理步数
            )

            generated_image = result.images[0]  # 获取生成的图像
            logger.debug(f"Generated Flux image size: {generated_image.size}")

            # 从生成的正方形图像中提取原始内容区域
            final_image = self._extract_from_square_flux(generated_image, content_area, original_size)
            logger.debug(f"Final extracted Flux image size: {final_image.size}")

            return final_image  # 返回最终图像

        except Exception as e:
            logger.error(f"Flux.1 editing failed: {e}")  # 记录编辑失败
            return image.copy()  # 返回原始图像的副本

    # ...existing code for other edit methods...

    def _get_aesthetic_score(self, image: Image.Image) -> float:
        """
        Get aesthetic score for an image - 获取图像的美学评分

        Args:
            image: Image to evaluate - 要评估的图像

        Returns:
            Aesthetic score (0-1) - 美学评分（0-1）
        """
        # 确保图像是RGB格式
        if image.mode != 'RGB':
            image = image.convert('RGB')
            
        if self.aesthetic_evaluator:  # 如果有美学评估器
            return self.aesthetic_evaluator.get_combined_score(image)  # 获取综合评分
        else:
            # Fallback to basic composition score - 回退到基本构图评分
            from ..utils.image_ops import ImageOperations
            scores = ImageOperations.calculate_composition_score(image)  # 计算构图评分
            return scores.get("overall", 0.5)  # 返回总体评分，默认0.5

    def _execute_plan_with_mllm(self, image: Image.Image, plan: AdjustmentPlan) -> BatchResult:
        """Execute plan using MLLM-based editing - 使用基于MLLM的编辑执行计划"""
        start_time = time.time()
        
        # 确保输入图像是RGB格式
        if image.mode != 'RGB':
            image = image.convert('RGB')
            
        # Initialize tracking variables - 初始化跟踪变量
        current_image = image.copy()  # 当前图像
        applied_tasks = []  # 已应用的任务
        rollback_count = 0  # 回滚计数
        best_image = image.copy()  # 最佳图像
        initial_score = self._get_aesthetic_score(image)  # 初始评分
        best_score = initial_score  # 最佳评分
        
        # Get rollback threshold - 获取回滚阈值
        rollback_threshold = getattr(self.profile.profile, 'rollback_threshold', 0.1) if self.profile.profile else 0.1
        
        logger.info(f"Starting MLLM restoration with {len(plan.instructions)} instructions")
        logger.info(f"Initial aesthetic score: {initial_score:.3f}")
        
        # Execute each instruction - 执行每个指令
        for i, instruction in enumerate(plan.instructions):
            instruction_start_time = time.time()  # 记录指令开始时间
            logger.info(f"Executing instruction {i+1}/{len(plan.instructions)}: {instruction.instruction}")
            
            # Get score before editing - 获取编辑前的评分
            score_before = self._get_aesthetic_score(current_image)
            
            # Try to execute the instruction with available models - 尝试使用可用模型执行指令
            edited_image = None  # 编辑后的图像
            model_used = None  # 使用的模型
            error_message = None  # 错误信息
            
            # Try models in priority order - 按优先级顺序尝试模型
            for model_name in self.mllm_models.keys():
                try:
                    editing_result = self._execute_mllm_edit(current_image, instruction.instruction, model_name)

                    if editing_result.success and editing_result.output_image:  # 如果编辑成功
                        edited_image = editing_result.output_image
                        model_used = model_name
                        break  # 成功后跳出循环
                except Exception:
                    continue  # 继续尝试下一个模型
            
            # Evaluate result - 评估结果
            if edited_image is not None:  # 如果编辑成功
                score_after = self._get_aesthetic_score(edited_image)  # 获取编辑后评分
                improvement = score_after - score_before  # 计算改进值
                
                # Decide whether to keep the edit - 决定是否保留编辑
                should_keep, reason = self._reflect_on_result(
                    ExecutionResult(
                        instruction=instruction.instruction,
                        input_image=current_image,
                        output_image=edited_image,
                        success=True,
                        score_before=score_before,
                        score_after=score_after,
                        improvement=improvement,
                        processing_time=time.time() - instruction_start_time
                    ),
                    rollback_threshold
                )
                
                if should_keep:  # 如果应该保留
                    current_image = edited_image  # 更新当前图像
                    if score_after > best_score:  # 如果评分更好
                        best_image = edited_image.copy()  # 更新最佳图像
                        best_score = score_after  # 更新最佳评分
                    success = True
                    logger.info(f"✓ Kept edit: {reason}")
                else:  # 如果应该回滚
                    rollback_count += 1  # 增加回滚计数
                    success = False
                    error_message = f"Rolled back: {reason}"
                    logger.info(f"✗ Rolled back: {reason}")
            else:
                # No model could process the instruction - 没有模型能处理该指令
                score_after = score_before  # 评分不变
                improvement = 0.0  # 无改进
                success = False
                error_message = "No available model could process this instruction"
                logger.warning(f"✗ Failed to process instruction: {error_message}")
            
            # Create execution result - 创建执行结果
            execution_result = ExecutionResult(
                success=success,
                message=error_message or "",
                instruction=instruction.instruction,
                input_image=current_image if edited_image is None else current_image,
                output_image=edited_image if edited_image is not None else current_image,
                score_before=score_before,
                score_after=score_after,
                improvement=improvement,
                processing_time=time.time() - instruction_start_time
            )
            applied_tasks.append(execution_result)  # 添加到已应用任务列表
        
        # Calculate final metrics - 计算最终指标
        total_time = time.time() - start_time  # 总时间
        successful_tasks = sum(1 for result in applied_tasks if result.success)  # 成功任务数
        success_rate = successful_tasks / len(applied_tasks) if applied_tasks else 0  # 成功率
        
        final_score = self._get_aesthetic_score(best_image)  # 最终评分
        total_improvement = final_score - initial_score  # 总改进值
        
        logger.info(f"MLLM restoration complete: Initial={initial_score:.3f}, Final={final_score:.3f}, Improvement={total_improvement:.3f}")
        
        # 返回批量操作结果
        return BatchResult(
            success=success_rate > 0,  # 至少有一个成功操作
            processing_time=total_time,
            original_image=image,
            final_image=best_image,
            operation_results=applied_tasks,
            total_improvement=total_improvement,
            final_score=final_score,
            original_score=initial_score
        )