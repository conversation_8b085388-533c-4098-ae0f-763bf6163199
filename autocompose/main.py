"""
Main AutoComposeAgent System - Pure Agent-R MCTS

Pure Agent-R implementation with MCTS trajectory exploration and rollback.
纯 Agent-R 实现，包含 MCTS 轨迹探索和回滚机制。
"""

# 设置 CUDA 确定性行为的环境变量（必须在导入 torch 之前设置）
import os  # 导入操作系统接口模块
if 'CUBLAS_WORKSPACE_CONFIG' not in os.environ:  # 检查环境变量是否已设置
    os.environ['CUBLAS_WORKSPACE_CONFIG'] = ':4096:8'  # 设置CUDA库的工作空间配置，确保计算结果的确定性
import logging  # 导入日志记录模块
from typing import Dict, List, Any, Optional, Union  # 导入类型提示
from PIL import Image  # 导入PIL图像处理库
from pathlib import Path  # 导入路径操作库
import time  # 导入时间模块
import contextlib  # 导入上下文管理器模块

from .core.profile import Profile  # 导入配置管理模块
from .agents.pure_agent_r import PureAgentRController, create_agent_r_controller  # 导入纯 Agent-R 控制器
from .utils.image_ops import ImageOperations  # 导入图像操作工具类
from .utils.logging_config import setup_logging, get_logger, log_system_info, LogContext  # 导入日志配置工具

logger = logging.getLogger(__name__)  # 获取当前模块的日志记录器


class AutoComposeAgent:
    """
    Pure Agent-R AutoComposeAgent system with MCTS
    纯 Agent-R AutoComposeAgent 系统，使用 MCTS
    """

    def __init__(self, config_path: Optional[str] = None):
        """
        Initialize Pure Agent-R AutoComposeAgent system
        初始化纯 Agent-R AutoComposeAgent 系统

        Args:
            config_path: Path to configuration file 配置文件路径
        """
        # Clean environment before loading models
        # 在加载模型之前清理环境
        from .utils.env_utils import validate_environment, suppress_bitsandbytes_warnings  # 导入环境工具
        validate_environment()  # 验证环境配置
        suppress_bitsandbytes_warnings()  # 抑制bitsandbytes库的警告信息

        # Load configuration
        # 加载配置
        config_path = config_path or 'config.yaml'  # 使用提供的配置路径或默认配置文件
        self.profile = Profile(config_path)  # 创建配置文件实例

        # Initialize pure Agent-R controller
        # 初始化纯 Agent-R 控制器
        from .core.evaluator import AestheticEvaluator
        from .agents.planning import UnifiedLLMClient

        # 创建 LLM 客户端
        llm_client = UnifiedLLMClient(self.profile)

        # 创建美学评估器
        aesthetic_models = self.profile.profile.aesthetic_models if self.profile.profile else ["shadow-aesthetic"]
        device = self.profile.profile.device if self.profile.profile else "cuda"
        random_seed = getattr(self.profile.profile, 'random_seed', 42) if self.profile.profile else 42
        aesthetic_evaluator = AestheticEvaluator(aesthetic_models, device, random_seed)

        self.agent_r_controller = create_agent_r_controller(
            llm_client,
            aesthetic_evaluator,
            self.profile
        )
        logger.info("Pure Agent-R controller initialized successfully")

        # Setup logging
        # 设置日志系统
        self._setup_logging()  # 调用日志设置函数

        logger.info("Pure Agent-R AutoComposeAgent initialized successfully")  # 记录AutoComposeAgent初始化成功
    
    def _setup_logging(self):
        """Setup comprehensive logging configuration 设置全面的日志配置"""
        if self.profile.logging:  # 如果配置中启用了日志
            loggers = setup_logging(  # 设置日志系统
                level=self.profile.logging.level,  # 设置日志级别
                enable_colors=True,  # 启用彩色日志
                enable_performance_logging=True  # 启用性能日志
            )
            log_system_info()  # 记录系统信息
            self.performance_logger = loggers.get('performance')  # 获取性能日志记录器
        else:
            logging.basicConfig(  # 基础日志配置
                level=logging.INFO,  # 设置日志级别为INFO
                format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'  # 设置日志格式
            )
            self.performance_logger = None  # 没有性能日志记录器

    @contextlib.contextmanager  # 上下文管理器装饰器
    def _timed_step(self, step_name, context_msg=None):
        """Context manager for timing and logging a processing step 用于计时和记录处理步骤的上下文管理器"""
        if context_msg:  # 如果有上下文消息
            logger.info(f"Step {context_msg}")  # 记录步骤信息
        
        context = LogContext(logger, step_name)  # 创建日志上下文
        context.__enter__()  # 进入上下文
        
        if self.performance_logger:  # 如果有性能日志记录器
            self.performance_logger.start_timer(step_name.lower())  # 开始计时
        
        try:
            yield  # 执行被管理的代码块
        finally:
            if self.performance_logger:  # 如果有性能日志记录器
                self.performance_logger.end_timer(step_name.lower())  # 结束计时

            context.__exit__(None, None, None)  # 退出上下文
    
    def process_image(self, input_path: Union[str, Path],
                     output_path: Optional[Union[str, Path]] = None,
                     save_intermediate: Optional[bool] = None) -> Dict[str, Any]:
        """
        Process a single image using pure Agent-R MCTS
        使用纯 Agent-R MCTS 处理单张图像

        Args:
            input_path: Path to input image 输入图像路径
            output_path: Path to save output image 输出图像保存路径
            save_intermediate: Whether to save intermediate results 是否保存中间结果

        Returns:
            Processing results dictionary 处理结果字典
        """
        start_time = time.time()  # 记录开始时间

        # Load image
        # 加载图像
        input_path = Path(input_path)  # 转换为Path对象
        if not input_path.exists():  # 检查文件是否存在
            raise FileNotFoundError(f"Input image not found: {input_path}")  # 抛出文件未找到异常

        image = Image.open(input_path).convert("RGB")  # 打开图像并转换为RGB格式
        logger.info(f"Processing image with Agent-R MCTS: {input_path} (size: {image.size})")  # 记录正在处理的图像信息

        # Process with Agent-R MCTS
        # 使用 Agent-R MCTS 处理
        with self._timed_step("Agent-R MCTS Processing", "Processing with Agent-R MCTS..."):  # 计时上下文管理器
            agent_r_result = self.agent_r_controller.process_image(image)  # 调用 Agent-R 控制器处理图像

        if not agent_r_result['success']:
            logger.error(f"Agent-R processing failed: {agent_r_result.get('error', 'Unknown error')}")
            raise RuntimeError(f"Agent-R processing failed: {agent_r_result.get('error', 'Unknown error')}")

        best_trajectory = agent_r_result['best_trajectory']
        mcts_stats = agent_r_result['mcts_stats']

        logger.info(f"Agent-R MCTS completed: {len(agent_r_result['all_trajectories']['success'])} success trajectories")
        logger.info(f"MCTS rollbacks: {mcts_stats['total_rollbacks']}")
        logger.info(f"Best trajectory: {best_trajectory.trajectory_id} (reward: {best_trajectory.total_reward:.3f})")

        # Prepare results
        # 准备结果
        results = {  # 构建结果字典
            "input_path": str(input_path),  # 输入路径
            "processing_time": time.time() - start_time,  # 处理时间
            "agent_r": {  # Agent-R 结果
                "success": agent_r_result['success'],  # 处理成功状态
                "best_trajectory": {
                    "trajectory_id": best_trajectory.trajectory_id,
                    "total_reward": best_trajectory.total_reward,
                    "length": best_trajectory.length,
                    "success": best_trajectory.success
                },
                "mcts_stats": mcts_stats,  # MCTS 统计信息
                "trajectory_counts": {
                    "success": len(agent_r_result['all_trajectories']['success']),
                    "failure": len(agent_r_result['all_trajectories']['failure']),
                    "revision": len(agent_r_result['all_trajectories']['revision']),
                    "neutral": len(agent_r_result['all_trajectories']['neutral'])
                },
                "processing_time": agent_r_result['processing_time']  # Agent-R 处理时间
            }
        }
        
        # Save output image if path provided
        # 如果提供了路径则保存输出图像
        if output_path:  # 如果有输出路径
            # 使用 Agent-R 处理后的最终图像
            final_image = agent_r_result.get('final_image', image)
            self._save_output_image(output_path, final_image, results)  # 保存输出图像

            # Handle intermediate results if needed
            # 如果需要则处理中间结果
            should_save_intermediate = save_intermediate  # 获取是否保存中间结果的设置
            if should_save_intermediate is None:  # 如果没有明确设置
                should_save_intermediate = self.profile.logging.save_intermediate if self.profile.logging else False  # 从配置获取

            if should_save_intermediate:  # 如果需要保存中间结果
                self._save_agent_r_results(Path(output_path), agent_r_result)  # 保存 Agent-R 结果
        
        return results  # 返回结果
    
    def _save_output_image(self, output_path: Union[str, Path], image: Image.Image, results: Dict[str, Any]):
        """Helper to save output image with proper format handling 保存输出图像的辅助函数，处理格式"""
        output_path = Path(output_path)  # 转换为Path对象
        output_path.parent.mkdir(parents=True, exist_ok=True)  # 创建输出目录
        
        # Get output format from profile or file extension
        # 从配置或文件扩展名获取输出格式
        output_format = self.profile.logging.output_format if self.profile.logging else "png"  # 获取输出格式
        if output_path.suffix:  # 如果有文件扩展名
            output_format = output_path.suffix[1:].lower()  # 使用文件扩展名作为格式
        
        image.save(output_path, format=output_format)  # 保存图像
        results["output_path"] = str(output_path)  # 将输出路径添加到结果中
        logger.info(f"Saved result to: {output_path}")  # 记录保存位置
    
    def _clean_filename(self, text: str, max_length: int = 50) -> str:
        """清理文本以用作文件名 Clean text for use as filename"""
        import re  # 导入正则表达式模块

        # 移除或替换不安全的字符
        # Remove or replace unsafe characters
        text = re.sub(r'[<>:"/\\|?*]', '_', text)  # 替换文件名中的不安全字符
        # 移除多余的空格和特殊字符
        # Remove extra spaces and special characters
        text = re.sub(r'[^\w\s-]', '', text)  # 移除非字母数字和空格的字符
        # 替换空格为下划线
        # Replace spaces with underscores
        text = re.sub(r'\s+', '_', text)  # 将空格替换为下划线
        # 截断到最大长度
        # Truncate to maximum length
        if len(text) > max_length:  # 如果文本太长
            text = text[:max_length].rstrip('_')  # 截断并移除末尾的下划线

        return text or "step"  # 如果清理后为空，使用默认名称
    

    
    def batch_process(self, input_dir: Union[str, Path],
                     output_dir: Union[str, Path],
                     pattern: str = "*.png") -> List[Dict[str, Any]]:
        """
        Process multiple images in batch using Agent-R MCTS
        使用 Agent-R MCTS 批量处理多张图像

        Args:
            input_dir: Directory containing input images 包含输入图像的目录
            output_dir: Directory to save output images 保存输出图像的目录
            pattern: File pattern to match 要匹配的文件模式

        Returns:
            List of processing results 处理结果列表
        """
        input_dir = Path(input_dir)  # 转换输入目录为Path对象
        output_dir = Path(output_dir)  # 转换输出目录为Path对象
        output_dir.mkdir(parents=True, exist_ok=True)  # 创建输出目录
        
        # Find input images
        # 查找输入图像
        image_files = list(input_dir.glob(pattern))  # 根据模式查找图像文件
        logger.info(f"Found {len(image_files)} images to process")  # 记录找到的图像数量
        
        results = []  # 结果列表
        
        for i, image_file in enumerate(image_files):  # 遍历图像文件
            logger.info(f"Processing {i+1}/{len(image_files)}: {image_file.name}")  # 记录处理进度
            
            try:
                output_file = output_dir / f"enhanced_{image_file.name}"  # 构建输出文件路径
                result = self.process_image(image_file, output_file)  # 处理图像
                result["success"] = True  # 标记成功
                results.append(result)  # 添加到结果列表
                
            except Exception as e:  # 捕获异常
                logger.error(f"Failed to process {image_file}: {e}")  # 记录错误
                results.append({  # 添加错误结果
                    "input_path": str(image_file),  # 输入路径
                    "error": str(e),  # 错误信息
                    "success": False  # 标记失败
                })
        
        # Generate summary
        # 生成摘要
        successful = sum(1 for r in results if r.get("success", False))  # 计算成功数量
        logger.info(f"Batch processing complete: {successful}/{len(results)} successful")  # 记录批处理完成情况
        
        return results  # 返回结果列表
    

    
    def _save_agent_r_results(self, output_path: Path, agent_r_result: Dict[str, Any]):
        """保存 Agent-R 结果的辅助函数"""
        base_path = output_path.with_suffix('')

        # 保存轨迹统计信息
        import json
        stats_path = f"{base_path}_agent_r_stats.json"
        with open(stats_path, 'w') as f:
            json.dump({
                'mcts_stats': agent_r_result['mcts_stats'],
                'trajectory_counts': {
                    'success': len(agent_r_result['all_trajectories']['success']),
                    'failure': len(agent_r_result['all_trajectories']['failure']),
                    'revision': len(agent_r_result['all_trajectories']['revision']),
                    'neutral': len(agent_r_result['all_trajectories']['neutral'])
                },
                'best_trajectory': {
                    'trajectory_id': agent_r_result['best_trajectory'].trajectory_id,
                    'total_reward': agent_r_result['best_trajectory'].total_reward,
                    'length': agent_r_result['best_trajectory'].length
                }
            }, f, indent=2)

        logger.info(f"Saved Agent-R results to {stats_path}")
    
    def get_system_info(self) -> Dict[str, Any]:
        """
        Get system configuration and status
        获取系统配置和状态

        Returns:
            System information dictionary 系统信息字典
        """
        return {  # 返回系统信息
            "profile": {  # 配置信息
                "aesthetic_models": self.profile.profile.aesthetic_models if self.profile.profile else None,  # 美学模型
                "device": self.profile.profile.device if self.profile.profile else None  # 设备
            },
            "agent_r": {  # Agent-R 状态
                "controller": "initialized",  # Agent-R 控制器已初始化
                "mcts_enabled": True,  # MCTS 已启用
                "rollback_enabled": True  # 回滚机制已启用
            },
            "config_path": str(self.profile.config_path) if self.profile.config_path else None  # 配置文件路径
        }
    
    def update_config(self, updates: Dict[str, Any]) -> None:
        """
        Update system configuration
        更新系统配置

        Args:
            updates: Configuration updates 配置更新
        """
        for section, section_updates in updates.items():  # 遍历更新项
            self.profile.update_config(section, section_updates)  # 更新配置

        # Reinitialize Agent-R controller if needed
        # 如果需要则重新初始化 Agent-R 控制器
        if "profile" in updates:  # 如果更新了profile配置
            from .core.evaluator import AestheticEvaluator
            from .agents.planning import UnifiedLLMClient

            llm_client = UnifiedLLMClient(self.profile)

            # 创建美学评估器
            aesthetic_models = self.profile.profile.aesthetic_models if self.profile.profile else ["shadow-aesthetic"]
            device = self.profile.profile.device if self.profile.profile else "cuda"
            random_seed = getattr(self.profile.profile, 'random_seed', 42) if self.profile.profile else 42
            aesthetic_evaluator = AestheticEvaluator(aesthetic_models, device, random_seed)

            self.agent_r_controller = create_agent_r_controller(
                llm_client,
                aesthetic_evaluator,
                self.profile
            )

        logger.info("Configuration updated and Agent-R controller reinitialized")  # 记录配置更新

    def get_agent_r_statistics(self) -> Dict[str, Any]:
        """
        Get Agent-R MCTS statistics
        获取 Agent-R MCTS 统计信息

        Returns:
            Agent-R statistics 统计信息
        """
        return self.agent_r_controller.get_statistics()

    def reset_agent_r(self):
        """
        Reset Agent-R controller state
        重置 Agent-R 控制器状态
        """
        self.agent_r_controller.reset()
        logger.info("Agent-R controller state reset")




def main():
    """Main entry point for command line usage 命令行使用的主入口点"""
    import sys  # 导入系统模块
    from .cli import cli  # 导入命令行接口

    # If called as module, use CLI
    # 如果作为模块调用，使用CLI
    if len(sys.argv) > 1:  # 如果有命令行参数
        cli()  # 调用CLI
    else:
        # Show help if no arguments
        # 如果没有参数则显示帮助
        cli(['--help'])  # 显示帮助信息


if __name__ == "__main__":  # 如果作为主程序运行
    main()  # 调用主函数
