# Intelligent Agent-R Configuration
# 智能 Agent-R 配置文件

profile:
  # 美学评估模型
  aesthetic_models:
    - "shadow-aesthetic"  # 主要美学评估模型
    - "imscore-v1"       # 辅助评估模型
  
  # 设备配置
  device: "cuda"  # 使用GPU加速
  
  # 随机种子
  random_seed: 42

# LLM 配置（用于智能动作生成）
llm:
  # 模型配置
  model_name: "Qwen2.5-7B-Instruct"  # 本地模型
  model_path: "/path/to/your/model"   # 模型路径
  
  # 生成参数
  max_tokens: 512
  temperature: 0.7
  top_p: 0.9
  
  # 智能动作生成专用提示
  action_generation:
    enabled: true
    max_actions: 8
    include_creative_effects: true
    focus_on_composition: true
    enable_semantic_understanding: true

# MCTS 配置
mcts:
  max_iterations: 50      # MCTS 迭代次数
  max_depth: 4           # 最大搜索深度
  rollback_threshold: -0.3  # 回滚阈值
  
  # 智能动作配置
  intelligent_actions:
    enabled: true
    use_llm_guidance: true    # 使用LLM指导
    fallback_to_rules: true   # LLM失败时回退到规则
    max_actions_per_image: 8  # 每张图像最大动作数

# 图像处理配置
image_processing:
  # 支持的操作类别
  enabled_categories:
    - "composition"        # 构图调整
    - "color_grading"     # 色彩分级
    - "local_adjustments" # 局部调整
    - "semantic_edits"    # 语义编辑
    - "creative_effects"  # 创意效果
  
  # 处理质量
  quality_level: "high"   # high, medium, fast
  
  # 保存中间结果
  save_intermediate_steps: true

# 日志配置
logging:
  level: "INFO"
  save_logs: true
  log_file: "agent_r_intelligent.log"
  
  # 详细日志模块（更新为新架构）
  detailed_modules:
    - "autocompose.agents.perception"
    - "autocompose.agents.planning"
    - "autocompose.agents.restoration"
    - "autocompose.agents.agent_r_mcts"
    - "autocompose.agents.pure_agent_r"
  
  # 保存中间结果
  save_intermediate: true
  save_tree_visualization: true

# 评估配置
evaluation:
  # 模型权重
  model_weights:
    shadow-aesthetic: 0.6
    imscore-v1: 0.4
  
  # 评估详细程度
  detailed_scoring: true
  save_evaluation_history: true

# 输出配置
output:
  # 保存格式
  format: "PNG"
  quality: 95
  
  # 保存额外信息
  save_metadata: true
  save_processing_stats: true
  save_action_history: true
  
  # 可视化
  create_before_after: true
  create_process_visualization: true
