#!/usr/bin/env python3
"""
Test Action Diversity

This script tests whether the vision-guided planner generates diverse actions.
"""

import sys
import logging
from pathlib import Path

# Add the current directory to the path
sys.path.insert(0, str(Path(__file__).parent))

from autocompose.agents.vision_guided_planner import VisionGuidedPlanner
from autocompose.agents.planning import UnifiedLLMClient
from autocompose.core.profile import Profile
from autocompose.utils.logging_config import setup_logging
from PIL import Image
import numpy as np

def test_action_generation():
    """测试动作生成的多样性"""
    print("=== Testing Action Generation Diversity ===")
    
    # 设置详细日志
    setup_logging(level=logging.INFO)
    logging.getLogger('autocompose.agents.vision_guided_planner').setLevel(logging.INFO)
    
    try:
        # 创建测试组件
        profile = Profile()
        llm_client = UnifiedLLMClient(profile)
        vision_planner = VisionGuidedPlanner(profile, llm_client)

        # 检查 LLM 是否可用
        if llm_client.is_available():
            print("✅ LLM client is available")
        else:
            print("⚠️  LLM client not available, will use fallback actions")

        # 检查 LLM 是否可用
        if llm_client.is_available():
            print("✅ LLM client is available")
        else:
            print("⚠️  LLM client not available, will use fallback actions")
        
        # 创建一个测试图像
        test_image = Image.new('RGB', (800, 600), color='red')
        
        print(f"\n🧪 Testing with synthetic image: {test_image.size}")
        
        # 测试动作生成
        print("\n🎯 Generating actions...")
        actions = vision_planner.generate_vision_guided_actions(test_image, max_actions=8)
        
        print(f"\n📊 Generated {len(actions)} actions:")
        print("=" * 60)
        
        action_names = []
        categories = set()
        
        for i, action in enumerate(actions, 1):
            print(f"{i}. 🎯 {action['name']}")
            print(f"   📝 {action['description']}")
            print(f"   🏷️  Category: {action['category']}")
            print(f"   ⭐ Priority: {action['priority']}")
            print(f"   💡 Reason: {action.get('reason', 'N/A')}")
            print()
            
            action_names.append(action['name'])
            categories.add(action['category'])
        
        # 分析多样性
        print("📈 Diversity Analysis:")
        print(f"  Unique actions: {len(set(action_names))}/{len(action_names)}")
        print(f"  Categories covered: {len(categories)}")
        print(f"  Categories: {', '.join(sorted(categories))}")
        
        # 检查是否有重复
        duplicates = [name for name in set(action_names) if action_names.count(name) > 1]
        if duplicates:
            print(f"  ⚠️  Duplicate actions: {duplicates}")
        else:
            print(f"  ✅ No duplicate actions")
        
        return len(set(action_names)) >= 4  # 至少4个不同的动作
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_fallback_actions():
    """测试回退动作生成"""
    print("\n=== Testing Fallback Action Generation ===")
    
    try:
        profile = Profile()
        llm_client = UnifiedLLMClient(profile)
        vision_planner = VisionGuidedPlanner(profile, llm_client)
        
        # 创建一个空的感知结果来触发回退
        from autocompose.agents.unified_types import PerceptionResult
        empty_perception = PerceptionResult(
            content_description="Test image",
            composition_issues=[],
            recommendations=[],
            overall_score=0.5
        )
        
        print("🔄 Testing fallback action generation...")
        fallback_actions = vision_planner._generate_fallback_actions(empty_perception)
        
        print(f"\n📊 Generated {len(fallback_actions)} fallback actions:")
        
        for i, action in enumerate(fallback_actions, 1):
            print(f"{i}. 🎯 {action['name']} ({action['category']})")
        
        # 检查多样性
        action_names = [action['name'] for action in fallback_actions]
        unique_count = len(set(action_names))
        
        print(f"\n📈 Fallback Diversity: {unique_count}/{len(action_names)} unique actions")
        
        return unique_count >= 4
        
    except Exception as e:
        print(f"❌ Fallback test failed: {e}")
        return False

def test_essential_actions():
    """测试基本动作集"""
    print("\n=== Testing Essential Actions ===")
    
    try:
        profile = Profile()
        llm_client = UnifiedLLMClient(profile)
        vision_planner = VisionGuidedPlanner(profile, llm_client)
        
        essential_actions = vision_planner._get_essential_actions()
        
        print(f"📚 Essential actions ({len(essential_actions)}):")
        
        categories = set()
        for i, action in enumerate(essential_actions, 1):
            print(f"{i}. 🎯 {action['name']} ({action['category']})")
            categories.add(action['category'])
        
        print(f"\n📊 Categories covered: {len(categories)}")
        print(f"Categories: {', '.join(sorted(categories))}")
        
        return len(essential_actions) >= 4 and len(categories) >= 3
        
    except Exception as e:
        print(f"❌ Essential actions test failed: {e}")
        return False

if __name__ == "__main__":
    print("🧪 Action Diversity Test Suite")
    print("=" * 50)
    
    tests = [
        ("Action Generation", test_action_generation),
        ("Fallback Actions", test_fallback_actions),
        ("Essential Actions", test_essential_actions)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n🔬 Running: {test_name}")
        result = test_func()
        results.append((test_name, result))
        print(f"Result: {'✅ PASS' if result else '❌ FAIL'}")
    
    print("\n" + "=" * 50)
    print("📊 Test Results Summary:")
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nOverall: {passed}/{len(tests)} tests passed")
    
    if passed == len(tests):
        print("🎉 All tests passed! Action diversity should be working correctly.")
    else:
        print("⚠️  Some tests failed. Action diversity may need further investigation.")
